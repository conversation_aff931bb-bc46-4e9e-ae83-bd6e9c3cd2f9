using System;
using System.Globalization;
using System.Windows;
using System.Windows.Data;

namespace DriverManagementSystem.Converters
{
    /// <summary>
    /// Converter لإظهار العنصر إذا كان النص فارغ أو null
    /// </summary>
    public class EmptyStringToVisibilityConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            string text = value as string;
            
            // إذا كان النص فارغ أو null، أظهر العنصر
            if (string.IsNullOrWhiteSpace(text))
            {
                return Visibility.Visible;
            }
            
            // إذا كان النص موجود، أخف العنصر
            return Visibility.Collapsed;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
