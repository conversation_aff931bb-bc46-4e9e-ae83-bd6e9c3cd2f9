using System;
using System.Globalization;
using System.Windows;
using System.Windows.Data;

namespace DriverManagementSystem.Converters
{
    /// <summary>
    /// Converter للتحقق من وجود النص أو الصورة وإظهار/إخفاء العناصر
    /// </summary>
    public class NullToVisibilityConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            bool isInverse = parameter?.ToString() == "Inverse";
            bool hasValue = !string.IsNullOrEmpty(value?.ToString());

            if (isInverse)
            {
                return hasValue ? Visibility.Collapsed : Visibility.Visible;
            }
            else
            {
                return hasValue ? Visibility.Visible : Visibility.Collapsed;
            }
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
