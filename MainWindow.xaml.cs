﻿using System;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using DriverManagementSystem.ViewModels;
using DriverManagementSystem.Services;
using DriverManagementSystem.Views;
using DriverManagementSystem.Data;
using DriverManagementSystem.Models;

namespace DriverManagementSystem;

public partial class MainWindow : Window
{
    private readonly MainViewModel _viewModel;
    private Services.AutoBackupService? _autoBackupService;

    public MainWindow()
    {
        try
        {
            System.Diagnostics.Debug.WriteLine("🏠 بدء إنشاء النافذة الرئيسية");

            System.Diagnostics.Debug.WriteLine("🔧 بدء تهيئة مكونات النافذة...");
            try
            {
                InitializeComponent();
                System.Diagnostics.Debug.WriteLine("✅ تم تهيئة مكونات النافذة");
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في InitializeComponent: {ex.Message}", ex);
            }

            System.Diagnostics.Debug.WriteLine("🔧 بدء إنشاء AuthenticationService...");
            try
            {
                var authService = new AuthenticationService();
                System.Diagnostics.Debug.WriteLine("✅ تم إنشاء AuthenticationService");

                System.Diagnostics.Debug.WriteLine("🔧 بدء إنشاء MainViewModel...");
                _viewModel = new MainViewModel(authService);
                System.Diagnostics.Debug.WriteLine("✅ تم إنشاء MainViewModel");
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في إنشاء Services/ViewModel: {ex.Message}", ex);
            }

            System.Diagnostics.Debug.WriteLine("🔧 بدء ربط DataContext...");
            DataContext = _viewModel;
            System.Diagnostics.Debug.WriteLine("✅ تم ربط DataContext");

            _viewModel.LogoutRequested += OnLogoutRequested;
            System.Diagnostics.Debug.WriteLine("✅ تم تسجيل أحداث تسجيل الخروج");

            // تأجيل تحميل البيانات الثقيلة إلى ما بعد ظهور النافذة
            System.Diagnostics.Debug.WriteLine("⏳ تأجيل تحميل البيانات إلى ما بعد ظهور النافذة");

            // تشغيل خدمة النسخ الاحتياطي التلقائي (بشكل آمن)
            System.Diagnostics.Debug.WriteLine("💾 تشغيل خدمة النسخ الاحتياطي");
            try
            {
                InitializeAutoBackupService();
            }
            catch (Exception backupEx)
            {
                System.Diagnostics.Debug.WriteLine($"⚠️ خطأ في خدمة النسخ الاحتياطي: {backupEx.Message}");
                // لا نوقف النافذة بسبب خطأ في النسخ الاحتياطي
            }

            System.Diagnostics.Debug.WriteLine("✅ تم إنشاء النافذة الرئيسية بنجاح");

            // إضافة معالج لحدث تحميل النافذة
            this.Loaded += MainWindow_Loaded;
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"❌ خطأ في إنشاء النافذة الرئيسية: {ex.Message}");
            System.Diagnostics.Debug.WriteLine($"❌ تفاصيل الخطأ: {ex.ToString()}");

            // محاولة إنشاء ViewModel بسيط في حالة الخطأ
            try
            {
                var fallbackAuthService = new AuthenticationService();
                _viewModel = new MainViewModel(fallbackAuthService);
                DataContext = _viewModel;
                _viewModel.LogoutRequested += OnLogoutRequested;
                System.Diagnostics.Debug.WriteLine("✅ تم إنشاء ViewModel احتياطي");
            }
            catch (Exception fallbackEx)
            {
                System.Diagnostics.Debug.WriteLine($"❌ فشل في إنشاء ViewModel احتياطي: {fallbackEx.Message}");

                // عرض رسالة خطأ مفصلة
                var errorMessage = $"خطأ خطير في إنشاء النافذة الرئيسية:\n\n{ex.Message}";
                if (ex.InnerException != null)
                {
                    errorMessage += $"\n\nالخطأ الداخلي:\n{ex.InnerException.Message}";
                }

                MessageBox.Show(errorMessage, "خطأ في النظام", MessageBoxButton.OK, MessageBoxImage.Error);

                // العودة إلى نافذة تسجيل الدخول
                var loginWindow = new Views.LoginWindow();
                loginWindow.Show();
                this.Close();
                return;
            }
        }
    }

    private void MainWindow_Loaded(object sender, RoutedEventArgs e)
    {
        try
        {
            System.Diagnostics.Debug.WriteLine("🏠 تم تحميل النافذة الرئيسية بنجاح");

            // تحميل البيانات في الخلفية بعد ظهور النافذة
            _ = Task.Run(async () =>
            {
                try
                {
                    System.Diagnostics.Debug.WriteLine("🔄 بدء تحميل البيانات في الخلفية...");
                    await InitializeDatabaseAsync();
                    System.Diagnostics.Debug.WriteLine("✅ تم تحميل البيانات بنجاح");
                }
                catch (Exception dbEx)
                {
                    System.Diagnostics.Debug.WriteLine($"⚠️ خطأ في تحميل البيانات: {dbEx.Message}");
                }
            });

            // تفعيل النماذج الاحترافية بعد تحميل النافذة
            _ = SafeInitializeProfessionalTemplatesAsync();
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"❌ خطأ في MainWindow_Loaded: {ex.Message}");
            // لا نوقف النافذة بسبب خطأ في التحميل
        }
    }

    private async Task InitializeDatabaseAsync()
    {
        try
        {
            System.Diagnostics.Debug.WriteLine("🌱 بدء تهيئة قاعدة البيانات (مبسطة)...");

            // فحص سريع لوجود البيانات الأساسية فقط
            using var dataService = new DatabaseService();

            // فحص سريع للقطاعات فقط
            var sectorsCount = await dataService.GetSectorsCountAsync();
            System.Diagnostics.Debug.WriteLine($"📋 عدد القطاعات: {sectorsCount}");

            // إضافة القطاعات الأساسية فقط إذا لم تكن موجودة
            if (sectorsCount == 0)
            {
                System.Diagnostics.Debug.WriteLine("🔧 إضافة القطاعات الأساسية...");
                var basicSeeder = new DatabaseSeeder();
                await basicSeeder.SeedSectorsAsync();
                basicSeeder.Dispose();
                System.Diagnostics.Debug.WriteLine("✅ تم إضافة القطاعات الأساسية");
            }

            // فحص سريع للمشاريع
            var projectsCount = await dataService.GetProjectsCountAsync();
            System.Diagnostics.Debug.WriteLine($"📁 عدد المشاريع: {projectsCount}");

            // إضافة المشاريع الأساسية فقط إذا لم تكن موجودة
            if (projectsCount == 0)
            {
                System.Diagnostics.Debug.WriteLine("🔧 إضافة المشاريع الأساسية...");
                var basicSeeder = new DatabaseSeeder();
                await basicSeeder.SeedProjectsAsync();
                basicSeeder.Dispose();
                System.Diagnostics.Debug.WriteLine("✅ تم إضافة المشاريع الأساسية");
            }

            System.Diagnostics.Debug.WriteLine("✅ تم الانتهاء من تهيئة قاعدة البيانات المبسطة");
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"❌ خطأ في تهيئة قاعدة البيانات: {ex.Message}");
            System.Diagnostics.Debug.WriteLine($"تفاصيل الخطأ: {ex.StackTrace}");
        }
    }

    private async Task SafeInitializeProfessionalTemplatesAsync()
    {
        try
        {
            System.Diagnostics.Debug.WriteLine("🎨 بدء تفعيل النماذج الاحترافية بطريقة آمنة...");

            // تفعيل النماذج الاحترافية بدون خدمة خارجية
            await Task.Run(() =>
            {
                try
                {
                    // تفعيل الإعدادات الأساسية
                    DriverManagementSystem.Properties.Settings.Default.ProfessionalTemplatesActivated = true;
                    DriverManagementSystem.Properties.Settings.Default.ActivationDate = DateTime.Now.ToString();
                    DriverManagementSystem.Properties.Settings.Default.Save();

                    System.Diagnostics.Debug.WriteLine("✅ تم تفعيل النماذج الاحترافية بنجاح!");
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"❌ خطأ في حفظ إعدادات النماذج الاحترافية: {ex.Message}");
                }
            });

            // تم إزالة الرسالة المزعجة
            System.Diagnostics.Debug.WriteLine("🎨 تم تفعيل النماذج الاحترافية بنجاح!");
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"❌ خطأ عام في تفعيل النماذج الاحترافية: {ex.Message}");
        }
    }

    private async Task InitializeProfessionalTemplatesAsync()
    {
        try
        {
            System.Diagnostics.Debug.WriteLine("🎨 بدء تفعيل النماذج الاحترافية...");

            var templatesService = new ProfessionalTemplatesService();

            // التحقق من حالة التفعيل السابقة
            if (templatesService.AreProfessionalTemplatesActivated())
            {
                System.Diagnostics.Debug.WriteLine("ℹ️ النماذج الاحترافية مفعلة مسبقاً");
                return;
            }

            // تفعيل النماذج الاحترافية
            var success = await templatesService.ActivateAllProfessionalTemplatesAsync();

            if (success)
            {
                System.Diagnostics.Debug.WriteLine("✅ تم تفعيل جميع النماذج الاحترافية بنجاح!");

                // تم إزالة الرسالة المزعجة نهائياً
                System.Diagnostics.Debug.WriteLine("✅ تم تفعيل جميع النماذج الاحترافية بنجاح!");
            }
            else
            {
                System.Diagnostics.Debug.WriteLine("⚠️ فشل في تفعيل بعض النماذج الاحترافية");
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"❌ خطأ في تفعيل النماذج الاحترافية: {ex.Message}");
        }
    }



    private void OnLogoutRequested(object? sender, System.EventArgs e)
    {
        var loginWindow = new Views.LoginWindow();
        loginWindow.Show();
        this.Close();
    }



    /// <summary>
    /// فتح نافذة إعداد قاعدة البيانات الاحترافية
    /// </summary>
    private void DatabaseSetupButton_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            var setupWindow = new Views.DatabaseConfigWindow();
            var result = setupWindow.ShowDialog();

            if (result == true)
            {
                MessageBox.Show("تم حفظ إعدادات قاعدة البيانات بنجاح!", "نجح",
                    MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في فتح نافذة إعداد قاعدة البيانات: {ex.Message}", "خطأ",
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    /// <summary>
    /// فتح نافذة إضافة الضباط الجدد
    /// </summary>
    private void AddOfficersButton_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            var addOfficersWindow = new Views.AddOfficersWindow();
            addOfficersWindow.ShowDialog();
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في فتح نافذة إضافة الضباط: {ex.Message}", "خطأ",
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    /// <summary>
    /// فتح نافذة استعادة قاعدة البيانات
    /// </summary>
    private void DatabaseRestoreButton_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            MessageBox.Show("ميزة استعادة قاعدة البيانات غير متوفرة حالياً", "معلومات",
                MessageBoxButton.OK, MessageBoxImage.Information);
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في فتح نافذة استعادة قاعدة البيانات: {ex.Message}", "خطأ",
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }



    /// <summary>
    /// تشغيل خدمة النسخ الاحتياطي التلقائي
    /// </summary>
    private void InitializeAutoBackupService()
    {
        try
        {
            _autoBackupService = new Services.AutoBackupService();
            _autoBackupService.Start();
            System.Diagnostics.Debug.WriteLine("🔄 تم تشغيل خدمة النسخ الاحتياطي التلقائي");
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"❌ خطأ في تشغيل خدمة النسخ الاحتياطي: {ex.Message}");
        }
    }

    protected override void OnClosing(System.ComponentModel.CancelEventArgs e)
    {
        try
        {
            System.Diagnostics.Debug.WriteLine("🔄 بدء عملية إغلاق النافذة الرئيسية");

            // تنظيف الموارد قبل الإغلاق
            if (_viewModel != null)
            {
                _viewModel.LogoutRequested -= OnLogoutRequested;
                System.Diagnostics.Debug.WriteLine("✅ تم إلغاء تسجيل أحداث ViewModel");
            }

            if (_autoBackupService != null)
            {
                try
                {
                    _autoBackupService.Stop();
                    _autoBackupService.Dispose();
                    _autoBackupService = null;
                    System.Diagnostics.Debug.WriteLine("✅ تم إغلاق خدمة النسخ الاحتياطي");
                }
                catch (Exception backupEx)
                {
                    System.Diagnostics.Debug.WriteLine($"⚠️ خطأ في إغلاق خدمة النسخ الاحتياطي: {backupEx.Message}");
                }
            }

            System.Diagnostics.Debug.WriteLine("✅ تم تنظيف جميع الموارد بنجاح");

            // السماح بالإغلاق
            base.OnClosing(e);
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"❌ خطأ أثناء إغلاق النافذة: {ex.Message}");
            // السماح بالإغلاق حتى لو حدث خطأ
            try
            {
                base.OnClosing(e);
            }
            catch
            {
                // تجاهل أي أخطاء إضافية
            }
        }
    }

    protected override void OnClosed(System.EventArgs e)
    {
        try
        {
            System.Diagnostics.Debug.WriteLine("✅ تم إغلاق النافذة الرئيسية نهائياً");

            // إنهاء التطبيق بالكامل
            Application.Current?.Shutdown();
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"❌ خطأ في إنهاء التطبيق: {ex.Message}");
            // محاولة إنهاء قسري للتطبيق
            try
            {
                Environment.Exit(0);
            }
            catch
            {
                // تجاهل أي أخطاء
            }
        }
        finally
        {
            try
            {
                base.OnClosed(e);
            }
            catch
            {
                // تجاهل أي أخطاء
            }
        }
    }

    /// <summary>
    /// فتح نافذة نظام إرسال الرسائل للسائقين المتنافسين
    /// </summary>
    private void OpenCompetitiveDriverMessagingWindow_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            System.Diagnostics.Debug.WriteLine("🎯 فتح نافذة نظام إرسال الرسائل للسائقين المتنافسين");
            var competitiveMessagingWindow = new Views.CompetitiveDriverMessagingWindow();
            competitiveMessagingWindow.Show();
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"❌ خطأ في فتح نافذة الرسائل التنافسية: {ex.Message}");
            MessageBox.Show($"خطأ في فتح نافذة نظام إرسال الرسائل للسائقين المتنافسين: {ex.Message}", "خطأ",
                          MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }
}