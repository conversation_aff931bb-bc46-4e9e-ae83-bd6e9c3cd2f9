<UserControl x:Class="DriverManagementSystem.Views.AssignmentView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:local="clr-namespace:DriverManagementSystem.Views"
             xmlns:converters="clr-namespace:DriverManagementSystem.Converters"
             FlowDirection="LeftToRight"
             Background="White">

    <UserControl.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="../Styles/PrintStyles.xaml"/>
            </ResourceDictionary.MergedDictionaries>

            <!-- Converter للتحقق من وجود الصور -->
            <BooleanToVisibilityConverter x:Key="BoolToVisibilityConverter"/>

            <!-- Converter مخصص للتحقق من وجود النص/الصورة -->
            <converters:NullToVisibilityConverter x:Key="NullToVisibilityConverter"/>

            <!-- Converter للتحقق من النص الفارغ -->
            <converters:EmptyStringToVisibilityConverter x:Key="EmptyStringToVisibilityConverter"/>

            <!-- Converter للرقم التسلسلي -->
            <converters:RowIndexConverter x:Key="RowIndexConverter"/>

        </ResourceDictionary>
    </UserControl.Resources>

    <!-- الإطار الخارجي الأسود بحجم A4 مصغر -->
    <Border BorderBrush="Black" BorderThickness="3" Margin="10" Background="White"
            Width="600" Height="850" HorizontalAlignment="Center" VerticalAlignment="Top">

        <!-- Grid للتخطيط مع Footer ثابت -->
        <Grid Background="Transparent" Margin="20,20,20,7">
            <Grid.RowDefinitions>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- المحتوى الرئيسي -->
            <ScrollViewer Grid.Row="0" VerticalScrollBarVisibility="Auto">
                <StackPanel FlowDirection="LeftToRight">

                <!-- الهيدر -->
            <Border Background="Transparent" BorderBrush="#CCCCCC" BorderThickness="0,0,0,1"
                    Padding="10" Margin="0,0,0,20">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <!-- الشعار -->
                    <Image Grid.Column="1" Source="../icons/sfd.png"
                           Width="91" Height="57"
                           VerticalAlignment="Top"
                           HorizontalAlignment="Left"
                           Margin="243,17,0,0" Grid.ColumnSpan="2"/>

                    <!-- النصوص الإنجليزية -->
                    <StackPanel Grid.Column="1" VerticalAlignment="Top" HorizontalAlignment="Left" Margin="4,10,0,0">
                        <TextBlock Text="Social Fund For Development"
                                   FontSize="12" FontWeight="Bold"
                                   Foreground="#666666" Margin="0,2"
                                   TextAlignment="Center"/>
                        <TextBlock Text="Republic OF YEMEN"
                                   FontSize="11"
                                   Foreground="#666666" Margin="0,1"
                                   TextAlignment="Center"/>
                        <TextBlock Text="Presidency of Council of Ministers"
                                   FontSize="10"
                                   Foreground="#666666" Margin="0,1"
                                   TextAlignment="Center"/>
                        <TextBlock Text="Dhamar &amp;Albidaa Branch"
                                   FontSize="10"
                                   Foreground="#666666" Margin="0,1"
                                   TextAlignment="Center"/>
                    </StackPanel>

                    <!-- النصوص العربية -->
                    <StackPanel Grid.Column="2" VerticalAlignment="Top" HorizontalAlignment="Left" Margin="157,10,0,0">
                        <TextBlock Text="الجمهورية اليمنية"
                                   FontSize="12" FontWeight="Bold"
                                   Foreground="#666666" Margin="0,2"
                                   TextAlignment="Center"/>
                        <TextBlock Text="رئاسة مجلس الوزراء"
                                   FontSize="11"
                                   Foreground="#666666" Margin="0,1"
                                   TextAlignment="Center"/>
                        <TextBlock Text="الصندوق الاجتماعي للتنمية"
                                   FontSize="10"
                                   Foreground="#666666" Margin="0,1"
                                   TextAlignment="Center"/>
                        <TextBlock Text="فرع ذمار البيضاء"
                                   FontSize="10"
                                   Foreground="#666666" Margin="0,1"
                                   TextAlignment="Center"/>
                    </StackPanel>
                </Grid>
            </Border>

                <!-- العنوان مع التاريخ -->

                <!-- النص التمهيدي -->
                <!-- العنوان مع التاريخ -->
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="277*"/>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="208*"/>
                        <ColumnDefinition Width="69*"/>
                    </Grid.ColumnDefinitions>

                    <!-- التاريخ الهجري -->
                    <StackPanel Grid.Column="0" HorizontalAlignment="Left" FlowDirection="LeftToRight">
                        <TextBlock Text="📅 الموافق:" FontSize="11" FontWeight="Bold" Foreground="#2196F3"/>
                        <TextBlock Text="{Binding HijriDate, FallbackValue=____ / __ / __}"
                                   FontSize="11" Foreground="#333"/>
                    </StackPanel>

                    <!-- العنوان -->
                    <Border Background="#2196F3" CornerRadius="15" Padding="20,8" HorizontalAlignment="Left" Width="114" Grid.ColumnSpan="3" Margin="240,20,0,0">
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="📋" FontSize="18" Margin="0,0,5,0"/>
                            <TextBlock Text="تكليف" FontSize="18" FontWeight="Bold"
                                       Foreground="White" TextAlignment="Center"/>
                        </StackPanel>
                    </Border>

                    <!-- التاريخ الميلادي -->
                    <StackPanel Grid.Column="3" HorizontalAlignment="Left" FlowDirection="RightToLeft" Margin="19,0,0,0">
                        <TextBlock Text="📅 التاريخ:" FontSize="11" FontWeight="Bold" Foreground="#2196F3"/>
                        <TextBlock Text="{Binding StartDate, FallbackValue=____/__/__}"
                                   FontSize="11" Foreground="#333"/>
                    </StackPanel>
                </Grid>

                <StackPanel Orientation="Horizontal" FlowDirection="RightToLeft" HorizontalAlignment="Center" Margin="0,10,0,10">
                    <TextBlock Text="يُكلف الصندوق الاجتماعي للتنمية فرع ذمار والبيضاء الأخ المُبين اسمه في الجدول أدناه لتنفيذ المهمة التالية:"
                               FlowDirection="RightToLeft" TextWrapping="Wrap" FontSize="16"
                               FontWeight="Bold" Foreground="#1976D2"
                               VerticalAlignment="Center" TextAlignment="Center"
                               Width="544" MaxWidth="600"/>
                </StackPanel>

                <!-- معلومات التكليف بشكل نقاط -->
                <StackPanel Margin="0,0,0,20" FlowDirection="RightToLeft" Height="114">

                    <!-- اسم المشروع -->
                    <StackPanel Orientation="Horizontal" Margin="0,0,0,8">
                        <TextBlock Text="•" FontSize="16" FontWeight="Bold" Foreground="#2196F3"
                                   Margin="0,0,10,0" VerticalAlignment="Top"/>
                        <StackPanel MaxWidth="500">
                            <TextBlock FontSize="12" Foreground="#495057" TextWrapping="Wrap" FlowDirection="RightToLeft"><Run Text="اسم المشروع: " FontWeight="Bold"/><Run Text=" "/><Run Text="{Binding ProjectName}" Foreground="#2196F3"/></TextBlock>
                        </StackPanel>
                    </StackPanel>

                    <!-- النشاط -->
                    <StackPanel Orientation="Horizontal" Margin="0,0,0,8">
                        <TextBlock Text="•" FontSize="16" FontWeight="Bold" Foreground="#28A745"
                                   Margin="0,0,10,0" VerticalAlignment="Top"/>
                        <TextBlock FontSize="12" Foreground="#495057" TextWrapping="Wrap" FlowDirection="RightToLeft"><Run Text="النشاط: " FontWeight="Bold"/><Run Text=" "/><Run Text="{Binding ActivityName}" Foreground="#28A745"/></TextBlock>
                    </StackPanel>

                    <!-- خط السير للنقاط الأمنية -->
                    <StackPanel Orientation="Horizontal" Margin="0,0,0,8">
                        <TextBlock Text="•" FontSize="16" FontWeight="Bold" Foreground="#FD7E14"
                                   Margin="0,0,10,0" VerticalAlignment="Top"/>
                        <TextBlock FontSize="12" Foreground="#495057" TextWrapping="Wrap" FlowDirection="RightToLeft"><Run Text="خط السير: " FontWeight="Bold"/><Run Text=" "/><Run Text="{Binding RouteName}" Foreground="#FD7E14"/></TextBlock>
                    </StackPanel>

                    <!-- تاريخ النزول والعودة -->
                    <StackPanel Orientation="Horizontal" Margin="0,0,0,8">
                        <TextBlock Text="•" FontSize="16" FontWeight="Bold" Foreground="#DC3545"
                                   Margin="0,0,10,0" VerticalAlignment="Top"/>
                        <TextBlock FontSize="12" Foreground="#495057" FlowDirection="RightToLeft"><Run Text="تاريخ النزول من: " FontWeight="Bold"/><Run Text=" "/><Run Text="{Binding StartDate}" Foreground="#DC3545" FontWeight="Bold"/><Run Text=" "/><Run Text="      حتى: " FontWeight="Bold"/><Run Text=" "/><Run Text="{Binding EndDate}" Foreground="#DC3545" FontWeight="Bold"/></TextBlock>
                    </StackPanel>

                </StackPanel>

                <!-- جدول المكلفين -->
                <Border Background="White" BorderBrush="Black" BorderThickness="1"
                        CornerRadius="0" Padding="0" Margin="0,-20,0,15">
                    <StackPanel>
                        <Border Background="White" BorderBrush="Black" BorderThickness="0,0,0,1" Padding="5">
                            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" FlowDirection="RightToLeft">
                                <TextBlock Text="القائم بالزيارة" FontSize="14" FontWeight="Bold"
                                           Foreground="Black"/>
                            </StackPanel>
                        </Border>

                        <DataGrid ItemsSource="{Binding Assignees}" AutoGenerateColumns="False"
                                  HeadersVisibility="Column" FontSize="12" RowHeight="30"
                                  BorderThickness="0" GridLinesVisibility="All"
                                  HorizontalGridLinesBrush="Black" VerticalGridLinesBrush="Black"
                                  Background="White" AlternatingRowBackground="White"
                                  CanUserAddRows="False" CanUserDeleteRows="False"
                                  MaxHeight="90" ScrollViewer.VerticalScrollBarVisibility="Hidden">
                            <DataGrid.Columns>
                                <!-- الصفة -->
                                <DataGridTextColumn Header="الصفة" Binding="{Binding Position}" Width="100">
                                    <DataGridTextColumn.HeaderStyle>
                                        <Style TargetType="{x:Type DataGridColumnHeader}">
                                            <Setter Property="Background" Value="White"/>
                                            <Setter Property="Foreground" Value="Black"/>
                                            <Setter Property="FontWeight" Value="Bold"/>
                                            <Setter Property="FontSize" Value="12"/>
                                            <Setter Property="HorizontalContentAlignment" Value="Center"/>
                                            <Setter Property="Padding" Value="5"/>
                                            <Setter Property="BorderBrush" Value="Black"/>
                                            <Setter Property="BorderThickness" Value="1"/>
                                        </Style>
                                    </DataGridTextColumn.HeaderStyle>
                                    <DataGridTextColumn.ElementStyle>
                                        <Style TargetType="{x:Type TextBlock}">
                                            <Setter Property="HorizontalAlignment" Value="Center"/>
                                            <Setter Property="VerticalAlignment" Value="Center"/>
                                            <Setter Property="FontWeight" Value="Normal"/>
                                        </Style>
                                    </DataGridTextColumn.ElementStyle>
                                </DataGridTextColumn>

                                <!-- نوع الهوية -->
                                <DataGridTextColumn Header="نوعها" Binding="{Binding CardType}" Width="100">
                                    <DataGridTextColumn.HeaderStyle>
                                        <Style TargetType="{x:Type DataGridColumnHeader}">
                                            <Setter Property="Background" Value="White"/>
                                            <Setter Property="Foreground" Value="Black"/>
                                            <Setter Property="FontWeight" Value="Bold"/>
                                            <Setter Property="FontSize" Value="12"/>
                                            <Setter Property="HorizontalContentAlignment" Value="Center"/>
                                            <Setter Property="Padding" Value="5"/>
                                            <Setter Property="BorderBrush" Value="Black"/>
                                            <Setter Property="BorderThickness" Value="1"/>
                                        </Style>
                                    </DataGridTextColumn.HeaderStyle>
                                    <DataGridTextColumn.ElementStyle>
                                        <Style TargetType="{x:Type TextBlock}">
                                            <Setter Property="HorizontalAlignment" Value="Center"/>
                                            <Setter Property="VerticalAlignment" Value="Center"/>
                                        </Style>
                                    </DataGridTextColumn.ElementStyle>
                                </DataGridTextColumn>

                                <!-- رقم الهوية -->
                                <DataGridTextColumn Header="رقم الهوية" Binding="{Binding CardNumber}" Width="120">
                                    <DataGridTextColumn.HeaderStyle>
                                        <Style TargetType="{x:Type DataGridColumnHeader}">
                                            <Setter Property="Background" Value="White"/>
                                            <Setter Property="Foreground" Value="Black"/>
                                            <Setter Property="FontWeight" Value="Bold"/>
                                            <Setter Property="FontSize" Value="12"/>
                                            <Setter Property="HorizontalContentAlignment" Value="Center"/>
                                            <Setter Property="Padding" Value="5"/>
                                            <Setter Property="BorderBrush" Value="Black"/>
                                            <Setter Property="BorderThickness" Value="1"/>
                                        </Style>
                                    </DataGridTextColumn.HeaderStyle>
                                    <DataGridTextColumn.ElementStyle>
                                        <Style TargetType="{x:Type TextBlock}">
                                            <Setter Property="HorizontalAlignment" Value="Center"/>
                                            <Setter Property="VerticalAlignment" Value="Center"/>
                                        </Style>
                                    </DataGridTextColumn.ElementStyle>
                                </DataGridTextColumn>

                                <!-- رقم التلفون -->
                                <DataGridTextColumn Header="رقم التلفون" Binding="{Binding PhoneNumber}" Width="120">
                                    <DataGridTextColumn.HeaderStyle>
                                        <Style TargetType="{x:Type DataGridColumnHeader}">
                                            <Setter Property="Background" Value="White"/>
                                            <Setter Property="Foreground" Value="Black"/>
                                            <Setter Property="FontWeight" Value="Bold"/>
                                            <Setter Property="FontSize" Value="12"/>
                                            <Setter Property="HorizontalContentAlignment" Value="Center"/>
                                            <Setter Property="Padding" Value="5"/>
                                            <Setter Property="BorderBrush" Value="Black"/>
                                            <Setter Property="BorderThickness" Value="1"/>
                                        </Style>
                                    </DataGridTextColumn.HeaderStyle>
                                    <DataGridTextColumn.ElementStyle>
                                        <Style TargetType="{x:Type TextBlock}">
                                            <Setter Property="HorizontalAlignment" Value="Center"/>
                                            <Setter Property="VerticalAlignment" Value="Center"/>
                                        </Style>
                                    </DataGridTextColumn.ElementStyle>
                                </DataGridTextColumn>

                                <!-- الاسم -->
                                <DataGridTextColumn Header="الاسم" Binding="{Binding Name}" Width="*">
                                    <DataGridTextColumn.HeaderStyle>
                                        <Style TargetType="{x:Type DataGridColumnHeader}">
                                            <Setter Property="Background" Value="White"/>
                                            <Setter Property="Foreground" Value="Black"/>
                                            <Setter Property="FontWeight" Value="Bold"/>
                                            <Setter Property="FontSize" Value="12"/>
                                            <Setter Property="HorizontalContentAlignment" Value="Center"/>
                                            <Setter Property="Padding" Value="5"/>
                                            <Setter Property="BorderBrush" Value="Black"/>
                                            <Setter Property="BorderThickness" Value="1"/>
                                        </Style>
                                    </DataGridTextColumn.HeaderStyle>
                                    <DataGridTextColumn.ElementStyle>
                                        <Style TargetType="{x:Type TextBlock}">
                                            <Setter Property="HorizontalAlignment" Value="Right"/>
                                            <Setter Property="VerticalAlignment" Value="Center"/>
                                            <Setter Property="Margin" Value="5,0"/>
                                        </Style>
                                    </DataGridTextColumn.ElementStyle>
                                </DataGridTextColumn>

                                <!-- م -->
                                <DataGridTextColumn Header="م" Width="40">
                                    <DataGridTextColumn.HeaderStyle>
                                        <Style TargetType="{x:Type DataGridColumnHeader}">
                                            <Setter Property="Background" Value="White"/>
                                            <Setter Property="Foreground" Value="Black"/>
                                            <Setter Property="FontWeight" Value="Bold"/>
                                            <Setter Property="FontSize" Value="12"/>
                                            <Setter Property="HorizontalContentAlignment" Value="Center"/>
                                            <Setter Property="Padding" Value="5"/>
                                            <Setter Property="BorderBrush" Value="Black"/>
                                            <Setter Property="BorderThickness" Value="1"/>
                                        </Style>
                                    </DataGridTextColumn.HeaderStyle>
                                    <DataGridTextColumn.Binding>
                                        <Binding RelativeSource="{RelativeSource AncestorType=DataGridRow}"
                                                 Converter="{StaticResource RowIndexConverter}"/>
                                    </DataGridTextColumn.Binding>
                                    <DataGridTextColumn.ElementStyle>
                                        <Style TargetType="{x:Type TextBlock}">
                                            <Setter Property="HorizontalAlignment" Value="Center"/>
                                            <Setter Property="VerticalAlignment" Value="Center"/>
                                            <Setter Property="FontWeight" Value="Bold"/>
                                        </Style>
                                    </DataGridTextColumn.ElementStyle>
                                </DataGridTextColumn>

                            </DataGrid.Columns>
                        </DataGrid>
                    </StackPanel>
                </Border>

                <!-- جدول المركبات -->
                <Border Background="White" BorderBrush="Black" BorderThickness="1"
                        CornerRadius="0" Padding="0" Margin="0,-10,0,25">
                    <StackPanel>
                        <!-- عنوان الجدول -->
                        <Border Background="White" BorderBrush="Black" BorderThickness="0,0,0,1" Padding="4">
                            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" FlowDirection="RightToLeft">
                                <TextBlock Text="وسائل النقل" FontSize="14" FontWeight="Bold"
                                           Foreground="Black"/>
                            </StackPanel>
                        </Border>

                        <!-- الجدول -->
                        <DataGrid ItemsSource="{Binding Vehicles}" AutoGenerateColumns="False"
                                  HeadersVisibility="Column" FontSize="12" RowHeight="30"
                                  BorderThickness="0" GridLinesVisibility="All"
                                  VerticalGridLinesBrush="Black"
                                  HorizontalGridLinesBrush="Black" Background="White"
                                  AlternatingRowBackground="White"
                                  CanUserAddRows="False" CanUserDeleteRows="False"
                                  MaxHeight="90" ScrollViewer.VerticalScrollBarVisibility="Hidden">
                            <DataGrid.Columns>
                                <DataGridTextColumn Header="رقم اللوحة" Binding="{Binding PlateNumber}" Width="100">
                                    <DataGridTextColumn.HeaderStyle>
                                        <Style TargetType="{x:Type DataGridColumnHeader}">
                                            <Setter Property="Background" Value="White"/>
                                            <Setter Property="Foreground" Value="Black"/>
                                            <Setter Property="FontWeight" Value="Bold"/>
                                            <Setter Property="FontSize" Value="12"/>
                                            <Setter Property="HorizontalContentAlignment" Value="Center"/>
                                            <Setter Property="Padding" Value="5"/>
                                            <Setter Property="BorderBrush" Value="Black"/>
                                            <Setter Property="BorderThickness" Value="1"/>
                                        </Style>
                                    </DataGridTextColumn.HeaderStyle>
                                    <DataGridTextColumn.ElementStyle>
                                        <Style TargetType="{x:Type TextBlock}">
                                            <Setter Property="HorizontalAlignment" Value="Center"/>
                                            <Setter Property="VerticalAlignment" Value="Center"/>
                                        </Style>
                                    </DataGridTextColumn.ElementStyle>
                                </DataGridTextColumn>
                                <DataGridTextColumn Header="نوع السيارة" Binding="{Binding VehicleType}" Width="100">
                                    <DataGridTextColumn.HeaderStyle>
                                        <Style TargetType="{x:Type DataGridColumnHeader}">
                                            <Setter Property="Background" Value="White"/>
                                            <Setter Property="Foreground" Value="Black"/>
                                            <Setter Property="FontWeight" Value="Bold"/>
                                            <Setter Property="FontSize" Value="12"/>
                                            <Setter Property="HorizontalContentAlignment" Value="Center"/>
                                            <Setter Property="Padding" Value="5"/>
                                            <Setter Property="BorderBrush" Value="Black"/>
                                            <Setter Property="BorderThickness" Value="1"/>
                                        </Style>
                                    </DataGridTextColumn.HeaderStyle>
                                    <DataGridTextColumn.ElementStyle>
                                        <Style TargetType="{x:Type TextBlock}">
                                            <Setter Property="HorizontalAlignment" Value="Center"/>
                                            <Setter Property="VerticalAlignment" Value="Center"/>
                                        </Style>
                                    </DataGridTextColumn.ElementStyle>
                                </DataGridTextColumn>
                                <DataGridTextColumn Header="رقم الهوية" Binding="{Binding DriverId}" Width="120">
                                    <DataGridTextColumn.HeaderStyle>
                                        <Style TargetType="{x:Type DataGridColumnHeader}">
                                            <Setter Property="Background" Value="White"/>
                                            <Setter Property="Foreground" Value="Black"/>
                                            <Setter Property="FontWeight" Value="Bold"/>
                                            <Setter Property="FontSize" Value="12"/>
                                            <Setter Property="HorizontalContentAlignment" Value="Center"/>
                                            <Setter Property="Padding" Value="5"/>
                                            <Setter Property="BorderBrush" Value="Black"/>
                                            <Setter Property="BorderThickness" Value="1"/>
                                        </Style>
                                    </DataGridTextColumn.HeaderStyle>
                                    <DataGridTextColumn.ElementStyle>
                                        <Style TargetType="{x:Type TextBlock}">
                                            <Setter Property="HorizontalAlignment" Value="Center"/>
                                            <Setter Property="VerticalAlignment" Value="Center"/>
                                        </Style>
                                    </DataGridTextColumn.ElementStyle>
                                </DataGridTextColumn>
                                <DataGridTextColumn Header="رقم الهاتف" Binding="{Binding Phone}" Width="120">
                                    <DataGridTextColumn.HeaderStyle>
                                        <Style TargetType="{x:Type DataGridColumnHeader}">
                                            <Setter Property="Background" Value="White"/>
                                            <Setter Property="Foreground" Value="Black"/>
                                            <Setter Property="FontWeight" Value="Bold"/>
                                            <Setter Property="FontSize" Value="12"/>
                                            <Setter Property="HorizontalContentAlignment" Value="Center"/>
                                            <Setter Property="Padding" Value="5"/>
                                            <Setter Property="BorderBrush" Value="Black"/>
                                            <Setter Property="BorderThickness" Value="1"/>
                                        </Style>
                                    </DataGridTextColumn.HeaderStyle>
                                    <DataGridTextColumn.ElementStyle>
                                        <Style TargetType="{x:Type TextBlock}">
                                            <Setter Property="HorizontalAlignment" Value="Center"/>
                                            <Setter Property="VerticalAlignment" Value="Center"/>
                                        </Style>
                                    </DataGridTextColumn.ElementStyle>
                                </DataGridTextColumn>
                                <DataGridTextColumn Header="اسم السائق" Binding="{Binding DriverName}" Width="*">
                                    <DataGridTextColumn.HeaderStyle>
                                        <Style TargetType="{x:Type DataGridColumnHeader}">
                                            <Setter Property="Background" Value="White"/>
                                            <Setter Property="Foreground" Value="Black"/>
                                            <Setter Property="FontWeight" Value="Bold"/>
                                            <Setter Property="FontSize" Value="12"/>
                                            <Setter Property="HorizontalContentAlignment" Value="Center"/>
                                            <Setter Property="Padding" Value="5"/>
                                            <Setter Property="BorderBrush" Value="Black"/>
                                            <Setter Property="BorderThickness" Value="1"/>
                                        </Style>
                                    </DataGridTextColumn.HeaderStyle>
                                    <DataGridTextColumn.ElementStyle>
                                        <Style TargetType="{x:Type TextBlock}">
                                            <Setter Property="HorizontalAlignment" Value="Right"/>
                                            <Setter Property="VerticalAlignment" Value="Center"/>
                                            <Setter Property="Margin" Value="5,0"/>
                                        </Style>
                                    </DataGridTextColumn.ElementStyle>
                                </DataGridTextColumn>

                                <DataGridTextColumn Header="م" Binding="{Binding Index}" Width="50">
                                    <DataGridTextColumn.HeaderStyle>
                                        <Style TargetType="{x:Type DataGridColumnHeader}">
                                            <Setter Property="Background" Value="White"/>
                                            <Setter Property="Foreground" Value="Black"/>
                                            <Setter Property="FontWeight" Value="Bold"/>
                                            <Setter Property="FontSize" Value="12"/>
                                            <Setter Property="HorizontalContentAlignment" Value="Center"/>
                                            <Setter Property="Padding" Value="5"/>
                                            <Setter Property="BorderBrush" Value="Black"/>
                                            <Setter Property="BorderThickness" Value="1"/>
                                        </Style>
                                    </DataGridTextColumn.HeaderStyle>
                                    <DataGridTextColumn.ElementStyle>
                                        <Style TargetType="{x:Type TextBlock}">
                                            <Setter Property="HorizontalAlignment" Value="Center"/>
                                            <Setter Property="VerticalAlignment" Value="Center"/>
                                            <Setter Property="FontWeight" Value="Normal"/>
                                        </Style>
                                    </DataGridTextColumn.ElementStyle>
                                </DataGridTextColumn>





                            </DataGrid.Columns>
                        </DataGrid>
                    </StackPanel>
                </Border>

                <!-- النص التعاوني -->
                <StackPanel Orientation="Horizontal" FlowDirection="RightToLeft" HorizontalAlignment="Center" Margin="0,-20,0,15">
                    <TextBlock Text="وعليه: تكرموا مشكورين بالتعاون مع المذكور لما فيه المصلحة العامة وخدمة المجتمع."
                               FlowDirection="RightToLeft" TextWrapping="Wrap" FontSize="14"
                               FontWeight="Bold" Foreground="#1976D2"
                               VerticalAlignment="Center" TextAlignment="Center"
                               Width="544" MaxWidth="600"/>
                </StackPanel>

                <!-- التوقيع -->
                <Border Background="Transparent" BorderBrush="Transparent" BorderThickness="0"
                        CornerRadius="8" Padding="20" Margin="0,20,0,0" Width="487">
                    <Grid FlowDirection="LeftToRight">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <!-- مدير الفرع -->
                        <StackPanel Grid.Column="0" HorizontalAlignment="Left">
                            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,0,0,10">
                                <TextBlock Text="مدير الفرع" FontWeight="Bold" FontSize="14"
                                           Foreground="#495057" Width="70"/>
                            </StackPanel>
                            <TextBlock Text="م/محمد محمد الديلمي" FontWeight="Bold" FontSize="13"
                                       Foreground="#2196F3" TextAlignment="Right"/>
                        </StackPanel>

                        <!-- التوقيع -->
                        <StackPanel Grid.Column="1" HorizontalAlignment="Center">
                            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,0,0,10"/>
                        </StackPanel>
                    </Grid>
                </Border>

                </StackPanel>
            </ScrollViewer>

            <!-- Footer ثابت في الأسفل -->
            <Border Grid.Row="1" Background="Transparent" BorderBrush="#DEE2E6"
                    CornerRadius="0" Padding="20" Margin="0,10,0,0">
                <StackPanel>
                    <!-- النص العربي -->
                    <TextBlock Text="الصندوق الاجتماعي للتنمية - فرع ذمار - خط صنعاء تعز - جولة كمران - عمارة الأوقاف - هاتف : 503045 / فاكس: 503047 / ص.ب: 87210"
                               FontSize="10" FontWeight="Bold" TextAlignment="Center"
                               Foreground="#2C3E50" Margin="0,2" FlowDirection="RightToLeft"
                               TextWrapping="Wrap"/>

                    <TextBlock Text="الرقم المجاني للشكاوي 8009800 أو الرقم المباشر بالشكاوي 770959624."
                               FontSize="10" TextAlignment="Center"
                               Foreground="#2C3E50" Margin="0,2" FlowDirection="RightToLeft"/>

                    <!-- النص الإنجليزي -->
                    <TextBlock FontSize="10" TextAlignment="Center" Foreground="#2C3E50"
                               Margin="0,2" FlowDirection="LeftToRight" TextWrapping="Wrap">
                        <Run Text="Social Fund for Development / Dhamar Branch – Taiz Sana'a Street- Camran Round – P.O. Box: 87400 "/>
                        <Run Text="Tel: 503045" Foreground="Blue" TextDecorations="Underline"/>
                        <Run Text=" – Fax: 503047 – Free number: 8009800 – Email: <EMAIL>"/>
                    </TextBlock>
                </StackPanel>
            </Border>

        </Grid>
    </Border>
</UserControl>
