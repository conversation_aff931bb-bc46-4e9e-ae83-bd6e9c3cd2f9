<Window x:Class="DriverManagementSystem.Views.CompetitiveDriverMessagingWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        mc:Ignorable="d"
        Title="نظام إرسال الرسائل للسائقين المتنافسين"
        Height="800" Width="1400"
        Background="#F8FAFC"
        FlowDirection="RightToLeft"
        WindowStartupLocation="CenterScreen"
        ResizeMode="CanResize"
        WindowState="Maximized">

    <Window.Resources>
        <!-- نمط الأزرار الرئيسية المحدث -->
        <Style x:Key="PrimaryButtonStyle" TargetType="Button">
            <Setter Property="Background">
                <Setter.Value>
                    <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                        <GradientStop Color="#4F46E5" Offset="0"/>
                        <GradientStop Color="#7C3AED" Offset="0.5"/>
                        <GradientStop Color="#EC4899" Offset="1"/>
                    </LinearGradientBrush>
                </Setter.Value>
            </Setter>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="30,20"/>
            <Setter Property="Margin" Value="15"/>
            <Setter Property="FontSize" Value="18"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="#6366F1" BlurRadius="25" ShadowDepth="8" Opacity="0.4"/>
                </Setter.Value>
            </Setter>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                CornerRadius="20"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                BorderBrush="{TemplateBinding BorderBrush}">
                            <ContentPresenter HorizontalAlignment="Center"
                                            VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background">
                                    <Setter.Value>
                                        <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                                            <GradientStop Color="#3730A3" Offset="0"/>
                                            <GradientStop Color="#6B21A8" Offset="0.5"/>
                                            <GradientStop Color="#BE185D" Offset="1"/>
                                        </LinearGradientBrush>
                                    </Setter.Value>
                                </Setter>
                                <Setter Property="Effect">
                                    <Setter.Value>
                                        <DropShadowEffect Color="#6366F1" BlurRadius="35" ShadowDepth="12" Opacity="0.6"/>
                                    </Setter.Value>
                                </Setter>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="RenderTransform">
                                    <Setter.Value>
                                        <ScaleTransform ScaleX="0.96" ScaleY="0.96"/>
                                    </Setter.Value>
                                </Setter>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- نمط بطاقات الإحصائيات المحدث -->
        <Style x:Key="StatCardStyle" TargetType="Border">
            <Setter Property="Background">
                <Setter.Value>
                    <LinearGradientBrush StartPoint="0,0" EndPoint="0,1">
                        <GradientStop Color="#FFFFFF" Offset="0"/>
                        <GradientStop Color="#F8FAFC" Offset="1"/>
                    </LinearGradientBrush>
                </Setter.Value>
            </Setter>
            <Setter Property="CornerRadius" Value="25"/>
            <Setter Property="Padding" Value="30"/>
            <Setter Property="Margin" Value="20"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="BorderBrush" Value="#E2E8F0"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="#CBD5E1" BlurRadius="30" ShadowDepth="8" Opacity="0.3"/>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- نمط النصوص الرئيسية المحدث -->
        <Style x:Key="HeaderTextStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="36"/>
            <Setter Property="FontWeight" Value="Black"/>
            <Setter Property="Foreground">
                <Setter.Value>
                    <LinearGradientBrush StartPoint="0,0" EndPoint="1,0">
                        <GradientStop Color="#1E293B" Offset="0"/>
                        <GradientStop Color="#475569" Offset="1"/>
                    </LinearGradientBrush>
                </Setter.Value>
            </Setter>
            <Setter Property="Margin" Value="0,0,0,30"/>
            <Setter Property="TextAlignment" Value="Center"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="#94A3B8" BlurRadius="5" ShadowDepth="2" Opacity="0.3"/>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- نمط النصوص الفرعية المحدث -->
        <Style x:Key="SubHeaderTextStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="18"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Foreground" Value="#64748B"/>
            <Setter Property="Margin" Value="0,0,0,15"/>
            <Setter Property="TextAlignment" Value="Center"/>
            <Setter Property="LineHeight" Value="28"/>
        </Style>

        <!-- نمط الأرقام الكبيرة المحدث -->
        <Style x:Key="BigNumberStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="56"/>
            <Setter Property="FontWeight" Value="Black"/>
            <Setter Property="HorizontalAlignment" Value="Center"/>
            <Setter Property="Margin" Value="0,15"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="#E2E8F0" BlurRadius="8" ShadowDepth="3" Opacity="0.5"/>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- نمط الأيقونات المحدث -->
        <Style x:Key="IconStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="42"/>
            <Setter Property="HorizontalAlignment" Value="Center"/>
            <Setter Property="Margin" Value="0,0,0,20"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="#E2E8F0" BlurRadius="10" ShadowDepth="4" Opacity="0.4"/>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- نمط أزرار الإحصائيات -->
        <Style x:Key="StatButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}">
                            <ContentPresenter/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#F7FAFC"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

    </Window.Resources>

    <Grid>
        <!-- الخلفية الرئيسية المحدثة -->
        <Border>
            <Border.Background>
                <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                    <GradientStop Color="#F8FAFC" Offset="0"/>
                    <GradientStop Color="#E2E8F0" Offset="0.3"/>
                    <GradientStop Color="#F1F5F9" Offset="0.7"/>
                    <GradientStop Color="#F8FAFC" Offset="1"/>
                </LinearGradientBrush>
            </Border.Background>
        </Border>

        <!-- المحتوى الرئيسي -->
        <ScrollViewer VerticalScrollBarVisibility="Auto" HorizontalScrollBarVisibility="Disabled" Padding="50">
            <StackPanel>

                <!-- العنوان الرئيسي المحدث -->
                <Border Background="White" CornerRadius="30" Padding="40,30" Margin="0,0,0,40"
                        BorderThickness="2" BorderBrush="#E2E8F0">
                    <Border.Effect>
                        <DropShadowEffect Color="#CBD5E1" BlurRadius="40" ShadowDepth="10" Opacity="0.3"/>
                    </Border.Effect>
                    <StackPanel>
                        <TextBlock Text="🎯 نظام إرسال الرسائل للسائقين المتنافسين"
                                  Style="{StaticResource HeaderTextStyle}"/>

                        <!-- معلومات الزيارة المحدثة -->
                        <Border Background="#F8FAFC" CornerRadius="15" Padding="25" Margin="0,20,0,0"
                                BorderThickness="1" BorderBrush="#E2E8F0">
                            <TextBlock Text="👥 قائمة السائقين المختارين للزيارة رقم: 1391-13333 • عبده علي محمد عايض • علي أحمد محمد المعظم • هارون عبدالولي أحمد علي الذهبي"
                                      Style="{StaticResource SubHeaderTextStyle}"
                                      TextWrapping="Wrap"/>
                        </Border>
                    </StackPanel>
                </Border>

                <!-- بطاقات الإحصائيات المحدثة -->
                <Grid Margin="0,0,0,50">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <!-- بطاقة إجمالي السائقين المحدثة -->
                    <Button Grid.Column="0" Style="{StaticResource StatButtonStyle}">
                        <Border Style="{StaticResource StatCardStyle}">
                            <StackPanel>
                                <Border Background="#3B82F6" CornerRadius="50" Width="80" Height="80" Margin="0,0,0,20">
                                    <TextBlock Text="👥" Style="{StaticResource IconStyle}" Foreground="White" VerticalAlignment="Center"/>
                                </Border>
                                <TextBlock Text="25" Style="{StaticResource BigNumberStyle}" Foreground="#3B82F6"/>
                                <TextBlock Text="إجمالي السائقين" Style="{StaticResource SubHeaderTextStyle}" Foreground="#1E293B"/>
                            </StackPanel>
                        </Border>
                    </Button>

                    <!-- بطاقة السائقين المتاحين المحدثة -->
                    <Button Grid.Column="1" Style="{StaticResource StatButtonStyle}">
                        <Border Style="{StaticResource StatCardStyle}">
                            <StackPanel>
                                <Border Background="#10B981" CornerRadius="50" Width="80" Height="80" Margin="0,0,0,20">
                                    <TextBlock Text="✅" Style="{StaticResource IconStyle}" Foreground="White" VerticalAlignment="Center"/>
                                </Border>
                                <TextBlock Text="20" Style="{StaticResource BigNumberStyle}" Foreground="#10B981"/>
                                <TextBlock Text="السائقين المتاحين" Style="{StaticResource SubHeaderTextStyle}" Foreground="#1E293B"/>
                            </StackPanel>
                        </Border>
                    </Button>

                    <!-- بطاقة السائقين المحددين المحدثة -->
                    <Button Grid.Column="2" Style="{StaticResource StatButtonStyle}">
                        <Border Style="{StaticResource StatCardStyle}">
                            <StackPanel>
                                <Border Background="#F59E0B" CornerRadius="50" Width="80" Height="80" Margin="0,0,0,20">
                                    <TextBlock Text="🎯" Style="{StaticResource IconStyle}" Foreground="White" VerticalAlignment="Center"/>
                                </Border>
                                <TextBlock Text="3" Style="{StaticResource BigNumberStyle}" Foreground="#F59E0B"/>
                                <TextBlock Text="السائقين المحددين" Style="{StaticResource SubHeaderTextStyle}" Foreground="#1E293B"/>
                            </StackPanel>
                        </Border>
                    </Button>

                    <!-- زر اختيار السائقين المحدث -->
                    <Button Grid.Column="3" Style="{StaticResource PrimaryButtonStyle}"
                            Height="220" Click="SelectDriversButton_Click">
                        <StackPanel>
                            <Border Background="White" CornerRadius="50" Width="80" Height="80" Margin="0,0,0,20">
                                <TextBlock Text="◀" FontSize="36" Foreground="#4F46E5" VerticalAlignment="Center" HorizontalAlignment="Center"/>
                            </Border>
                            <TextBlock Text="اختيار سائقين" FontSize="22" FontWeight="Bold" Foreground="White" HorizontalAlignment="Center"/>
                        </StackPanel>
                    </Button>

                </Grid>

                <!-- شريط التقدم -->
                <Border Background="White" CornerRadius="15" Padding="30" Margin="0,0,0,30"
                        Effect="{StaticResource {x:Static SystemParameters.DropShadowKey}}">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <!-- مرحلة اختيار السائقين -->
                        <StackPanel Grid.Column="0" Orientation="Horizontal" HorizontalAlignment="Center">
                            <Border Background="#00BCD4" CornerRadius="50" Width="60" Height="8" Margin="0,0,10,0"/>
                            <TextBlock Text="اختيار السائقين" FontSize="14" FontWeight="SemiBold" VerticalAlignment="Center"/>
                        </StackPanel>

                        <!-- مرحلة إرسال الرسائل -->
                        <StackPanel Grid.Column="1" Orientation="Horizontal" HorizontalAlignment="Center">
                            <Border Background="#9E9E9E" CornerRadius="50" Width="60" Height="8" Margin="0,0,10,0"/>
                            <TextBlock Text="إرسال الرسائل" FontSize="14" FontWeight="SemiBold" VerticalAlignment="Center" Foreground="#9E9E9E"/>
                        </StackPanel>

                        <!-- مرحلة متابعة الردود -->
                        <StackPanel Grid.Column="2" Orientation="Horizontal" HorizontalAlignment="Center">
                            <Border Background="#4CAF50" CornerRadius="50" Width="60" Height="8" Margin="0,0,10,0"/>
                            <TextBlock Text="متابعة الردود" FontSize="14" FontWeight="SemiBold" VerticalAlignment="Center"/>
                        </StackPanel>

                    </Grid>
                </Border>

                <!-- جدول السائقين المحدث -->
                <Border Background="White" CornerRadius="30" Padding="40" Margin="0,0,0,50"
                        BorderThickness="2" BorderBrush="#E2E8F0">
                    <Border.Effect>
                        <DropShadowEffect Color="#CBD5E1" BlurRadius="40" ShadowDepth="10" Opacity="0.3"/>
                    </Border.Effect>

                    <StackPanel>
                        <Border Background="#F8FAFC" CornerRadius="20" Padding="30,20" Margin="0,0,0,30"
                                BorderThickness="1" BorderBrush="#E2E8F0">
                            <TextBlock Text="🏆 السائقين الذين تم تقديم أسعارهم"
                                      FontSize="28" FontWeight="Black"
                                      Foreground="#1E293B"
                                      HorizontalAlignment="Center"/>
                        </Border>

                        <Border Background="#F8FAFC" CornerRadius="20" Padding="20" BorderThickness="1" BorderBrush="#E2E8F0">
                            <DataGrid x:Name="DriversDataGrid"
                                     AutoGenerateColumns="False"
                                     CanUserAddRows="False"
                                     CanUserDeleteRows="False"
                                     CanUserReorderColumns="False"
                                     CanUserResizeColumns="True"
                                     CanUserSortColumns="True"
                                     GridLinesVisibility="None"
                                     HeadersVisibility="Column"
                                     SelectionMode="Extended"
                                     Background="Transparent"
                                     BorderThickness="0"
                                     RowBackground="White"
                                     AlternatingRowBackground="#F8FAFC"
                                     FontSize="16"
                                     Height="350">

                                <DataGrid.ColumnHeaderStyle>
                                    <Style TargetType="DataGridColumnHeader">
                                        <Setter Property="Background">
                                            <Setter.Value>
                                                <LinearGradientBrush StartPoint="0,0" EndPoint="0,1">
                                                    <GradientStop Color="#1E293B" Offset="0"/>
                                                    <GradientStop Color="#334155" Offset="1"/>
                                                </LinearGradientBrush>
                                            </Setter.Value>
                                        </Setter>
                                        <Setter Property="Foreground" Value="White"/>
                                        <Setter Property="FontWeight" Value="Bold"/>
                                        <Setter Property="FontSize" Value="16"/>
                                        <Setter Property="Padding" Value="20,15"/>
                                        <Setter Property="HorizontalContentAlignment" Value="Center"/>
                                        <Setter Property="BorderThickness" Value="0,0,1,0"/>
                                        <Setter Property="BorderBrush" Value="#475569"/>
                                    </Style>
                                </DataGrid.ColumnHeaderStyle>

                                <DataGrid.CellStyle>
                                    <Style TargetType="DataGridCell">
                                        <Setter Property="Padding" Value="20,15"/>
                                        <Setter Property="BorderThickness" Value="0"/>
                                        <Setter Property="HorizontalAlignment" Value="Center"/>
                                        <Setter Property="VerticalAlignment" Value="Center"/>
                                        <Setter Property="FontSize" Value="15"/>
                                        <Style.Triggers>
                                            <Trigger Property="IsSelected" Value="True">
                                                <Setter Property="Background" Value="#3B82F6"/>
                                                <Setter Property="Foreground" Value="White"/>
                                            </Trigger>
                                        </Style.Triggers>
                                    </Style>
                                </DataGrid.CellStyle>

                                <DataGrid.RowStyle>
                                    <Style TargetType="DataGridRow">
                                        <Setter Property="Height" Value="60"/>
                                        <Style.Triggers>
                                            <Trigger Property="IsMouseOver" Value="True">
                                                <Setter Property="Background" Value="#E0E7FF"/>
                                            </Trigger>
                                            <Trigger Property="IsSelected" Value="True">
                                                <Setter Property="Background" Value="#3B82F6"/>
                                                <Setter Property="Foreground" Value="White"/>
                                            </Trigger>
                                        </Style.Triggers>
                                    </Style>
                                </DataGrid.RowStyle>

                                <DataGrid.Columns>
                                    <DataGridTextColumn Header="اسم السائق" Binding="{Binding DriverName}" Width="*"/>
                                    <DataGridTextColumn Header="السعر المقدم" Binding="{Binding Price}" Width="150"/>
                                    <DataGridTextColumn Header="الحالة" Binding="{Binding Status}" Width="120"/>
                                    <DataGridTextColumn Header="تاريخ التقديم" Binding="{Binding SubmissionDate}" Width="150"/>
                                    <DataGridTemplateColumn Header="نص الرسالة" Width="100">
                                        <DataGridTemplateColumn.CellTemplate>
                                            <DataTemplate>
                                                <Button Content="📄" Background="Transparent" BorderThickness="0"
                                                       FontSize="20" Cursor="Hand" ToolTip="عرض نص الرسالة"
                                                       Click="ViewMessageButton_Click"/>
                                            </DataTemplate>
                                        </DataGridTemplateColumn.CellTemplate>
                                    </DataGridTemplateColumn>
                                </DataGrid.Columns>
                            </DataGrid>
                        </Border>
                    </StackPanel>
                </Border>

                <!-- قسم الأزرار الرئيسية المحدث -->
                <Border Background="White" CornerRadius="30" Padding="50" Margin="0,0,0,40"
                        BorderThickness="2" BorderBrush="#E2E8F0">
                    <Border.Effect>
                        <DropShadowEffect Color="#CBD5E1" BlurRadius="40" ShadowDepth="10" Opacity="0.3"/>
                    </Border.Effect>
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <!-- زر إرسال رسائل جماعية محدث -->
                        <Button Grid.Column="0" Style="{StaticResource PrimaryButtonStyle}"
                                Height="150" Click="SendGroupMessagesButton_Click">
                            <StackPanel>
                                <Border Background="White" CornerRadius="50" Width="80" Height="80" Margin="0,0,0,20">
                                    <TextBlock Text="📱" FontSize="36" Foreground="#4F46E5" VerticalAlignment="Center" HorizontalAlignment="Center"/>
                                </Border>
                                <TextBlock Text="إرسال رسائل جماعية" FontSize="18" FontWeight="Bold" Foreground="White" HorizontalAlignment="Center"/>
                            </StackPanel>
                        </Button>

                        <!-- زر متابعة الردود محدث -->
                        <Button Grid.Column="1" Style="{StaticResource PrimaryButtonStyle}"
                                Height="150" Click="TrackResponsesButton_Click">
                            <StackPanel>
                                <Border Background="White" CornerRadius="50" Width="80" Height="80" Margin="0,0,0,20">
                                    <TextBlock Text="📊" FontSize="36" Foreground="#4F46E5" VerticalAlignment="Center" HorizontalAlignment="Center"/>
                                </Border>
                                <TextBlock Text="متابعة الردود" FontSize="18" FontWeight="Bold" Foreground="White" HorizontalAlignment="Center"/>
                            </StackPanel>
                        </Button>

                        <!-- زر إعدادات الرسائل محدث -->
                        <Button Grid.Column="2" Style="{StaticResource PrimaryButtonStyle}"
                                Height="150" Click="MessageSettingsButton_Click">
                            <StackPanel>
                                <Border Background="White" CornerRadius="50" Width="80" Height="80" Margin="0,0,0,20">
                                    <TextBlock Text="⚙️" FontSize="36" Foreground="#4F46E5" VerticalAlignment="Center" HorizontalAlignment="Center"/>
                                </Border>
                                <TextBlock Text="إعدادات الرسائل" FontSize="18" FontWeight="Bold" Foreground="White" HorizontalAlignment="Center"/>
                            </StackPanel>
                        </Button>

                    </Grid>
                </Border>

                <!-- قسم الإجراءات السريعة المحدث -->
                <Border Background="White" CornerRadius="30" Padding="40" Margin="0,0,0,40"
                        BorderThickness="2" BorderBrush="#E2E8F0">
                    <Border.Effect>
                        <DropShadowEffect Color="#CBD5E1" BlurRadius="40" ShadowDepth="10" Opacity="0.3"/>
                    </Border.Effect>
                    <StackPanel>
                        <Border Background="#F8FAFC" CornerRadius="20" Padding="25,15" Margin="0,0,0,30"
                                BorderThickness="1" BorderBrush="#E2E8F0">
                            <TextBlock Text="⚡ الإجراءات السريعة" FontSize="24" FontWeight="Black"
                                      Foreground="#1E293B" HorizontalAlignment="Center"/>
                        </Border>

                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <!-- زر تحديث البيانات محدث -->
                            <Button Grid.Column="0" Style="{StaticResource PrimaryButtonStyle}"
                                    Height="100" Click="RefreshDataButton_Click">
                                <StackPanel>
                                    <Border Background="White" CornerRadius="30" Width="50" Height="50" Margin="0,0,0,10">
                                        <TextBlock Text="🔄" FontSize="24" Foreground="#4F46E5" VerticalAlignment="Center" HorizontalAlignment="Center"/>
                                    </Border>
                                    <TextBlock Text="تحديث البيانات" FontSize="14" FontWeight="Bold" Foreground="White" HorizontalAlignment="Center"/>
                                </StackPanel>
                            </Button>

                            <!-- زر تصدير التقرير محدث -->
                            <Button Grid.Column="1" Style="{StaticResource PrimaryButtonStyle}"
                                    Height="100" Click="ExportReportButton_Click">
                                <StackPanel>
                                    <Border Background="White" CornerRadius="30" Width="50" Height="50" Margin="0,0,0,10">
                                        <TextBlock Text="📄" FontSize="24" Foreground="#4F46E5" VerticalAlignment="Center" HorizontalAlignment="Center"/>
                                    </Border>
                                    <TextBlock Text="تصدير التقرير" FontSize="14" FontWeight="Bold" Foreground="White" HorizontalAlignment="Center"/>
                                </StackPanel>
                            </Button>

                            <!-- زر مسح الاختيار محدث -->
                            <Button Grid.Column="2" Style="{StaticResource PrimaryButtonStyle}"
                                    Height="100" Click="ClearSelectionButton_Click">
                                <StackPanel>
                                    <Border Background="White" CornerRadius="30" Width="50" Height="50" Margin="0,0,0,10">
                                        <TextBlock Text="🗑️" FontSize="24" Foreground="#4F46E5" VerticalAlignment="Center" HorizontalAlignment="Center"/>
                                    </Border>
                                    <TextBlock Text="مسح الاختيار" FontSize="14" FontWeight="Bold" Foreground="White" HorizontalAlignment="Center"/>
                                </StackPanel>
                            </Button>

                            <!-- زر إغلاق محدث -->
                            <Button Grid.Column="3" Style="{StaticResource PrimaryButtonStyle}"
                                    Height="100" Click="CloseButton_Click">
                                <StackPanel>
                                    <Border Background="White" CornerRadius="30" Width="50" Height="50" Margin="0,0,0,10">
                                        <TextBlock Text="❌" FontSize="24" Foreground="#4F46E5" VerticalAlignment="Center" HorizontalAlignment="Center"/>
                                    </Border>
                                    <TextBlock Text="إغلاق" FontSize="14" FontWeight="Bold" Foreground="White" HorizontalAlignment="Center"/>
                                </StackPanel>
                            </Button>

                        </Grid>
                    </StackPanel>
                </Border>

            </StackPanel>
        </ScrollViewer>
    </Grid>
</Window>
