using System;
using System.Windows;

namespace DriverManagementSystem.Views
{
    /// <summary>
    /// نافذة نظام إرسال الرسائل للسائقين المتنافسين
    /// </summary>
    public partial class CompetitiveDriverMessagingWindow : Window
    {
        public CompetitiveDriverMessagingWindow()
        {
            InitializeComponent();
            System.Diagnostics.Debug.WriteLine("🎯 تم إنشاء نافذة نظام إرسال الرسائل للسائقين المتنافسين");
        }

        /// <summary>
        /// معالج النقر على زر اختيار السائقين
        /// </summary>
        private void SelectDriversButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("🔘 تم النقر على زر اختيار السائقين");
                
                // هنا يمكن إضافة منطق فتح نافذة اختيار السائقين
                MessageBox.Show("سيتم فتح نافذة اختيار السائقين", "اختيار السائقين", 
                              MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في زر اختيار السائقين: {ex.Message}");
                MessageBox.Show($"خطأ: {ex.Message}", "خطأ", 
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// معالج النقر على زر عرض نص الرسالة
        /// </summary>
        private void ViewMessageButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("📄 تم النقر على زر عرض نص الرسالة");

                // الحصول على البيانات من الصف المحدد
                var button = sender as System.Windows.Controls.Button;
                var dataContext = button?.DataContext;

                if (dataContext != null)
                {
                    // استخراج اسم السائق ونص الرسالة من البيانات
                    var driverName = GetPropertyValue(dataContext, "DriverName")?.ToString() ?? "غير محدد";
                    var messageText = GenerateSampleMessage(driverName);

                    // فتح نافذة عرض الرسالة
                    var messageWindow = new SFDSystem.Views.MessageViewWindow(driverName, messageText);
                    messageWindow.ShowDialog();
                }
                else
                {
                    MessageBox.Show("⚠️ لا يمكن الحصول على بيانات السائق", "تنبيه",
                                  MessageBoxButton.OK, MessageBoxImage.Warning);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في عرض نص الرسالة: {ex.Message}");
                MessageBox.Show($"خطأ في عرض نص الرسالة: {ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// الحصول على قيمة خاصية من كائن
        /// </summary>
        private object GetPropertyValue(object obj, string propertyName)
        {
            try
            {
                var property = obj.GetType().GetProperty(propertyName);
                return property?.GetValue(obj);
            }
            catch
            {
                return null;
            }
        }

        /// <summary>
        /// توليد نص رسالة تجريبي
        /// </summary>
        private string GenerateSampleMessage(string driverName)
        {
            return $@"السلام عليكم ورحمة الله وبركاته

الأخ الكريم/ {driverName} المحترم

نحيطكم علماً بأنه تم اختياركم للمشاركة في الزيارة الميدانية رقم: 1391-13333

تفاصيل الزيارة:
📅 تاريخ البداية: من 2024/01/15
📅 تاريخ النهاية: حتى 2024/01/20
🗺️ المناطق المحددة: منطقة الرياض - منطقة مكة المكرمة
⏰ مدة الزيارة: 5 أيام

برنامج الزيارة:
🔹 اليوم الأول: الانطلاق من الرياض إلى مكة المكرمة
🔹 اليوم الثاني: زيارة المشاريع في منطقة مكة
🔹 اليوم الثالث: مراجعة التقارير والمتابعة
🔹 اليوم الرابع: زيارات ميدانية إضافية
🔹 اليوم الخامس: العودة إلى الرياض

يرجى تقديم عرض السعر المناسب لهذه الزيارة مع مراعاة:
✅ تكاليف الوقود
✅ تكاليف الإقامة
✅ أجرة السائق
✅ أي تكاليف إضافية

نتطلع لتعاونكم معنا.

مع أطيب التحيات
إدارة النظام";
        }

        /// <summary>
        /// معالج النقر على زر إرسال رسائل جماعية
        /// </summary>
        private void SendGroupMessagesButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("📱 تم النقر على زر إرسال رسائل جماعية");
                MessageBox.Show("سيتم فتح نافذة إرسال الرسائل الجماعية", "إرسال رسائل جماعية",
                              MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في زر إرسال رسائل جماعية: {ex.Message}");
                MessageBox.Show($"خطأ: {ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// معالج النقر على زر متابعة الردود
        /// </summary>
        private void TrackResponsesButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("📊 تم النقر على زر متابعة الردود");
                MessageBox.Show("سيتم فتح نافذة متابعة الردود", "متابعة الردود",
                              MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في زر متابعة الردود: {ex.Message}");
                MessageBox.Show($"خطأ: {ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// معالج النقر على زر إعدادات الرسائل
        /// </summary>
        private void MessageSettingsButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("⚙️ تم النقر على زر إعدادات الرسائل");
                MessageBox.Show("سيتم فتح نافذة إعدادات الرسائل", "إعدادات الرسائل",
                              MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في زر إعدادات الرسائل: {ex.Message}");
                MessageBox.Show($"خطأ: {ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// معالج النقر على زر تحديث البيانات
        /// </summary>
        private void RefreshDataButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("🔄 تم النقر على زر تحديث البيانات");
                MessageBox.Show("تم تحديث البيانات بنجاح", "تحديث البيانات",
                              MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في زر تحديث البيانات: {ex.Message}");
                MessageBox.Show($"خطأ: {ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// معالج النقر على زر تصدير التقرير
        /// </summary>
        private void ExportReportButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("📄 تم النقر على زر تصدير التقرير");
                MessageBox.Show("سيتم تصدير التقرير", "تصدير التقرير",
                              MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في زر تصدير التقرير: {ex.Message}");
                MessageBox.Show($"خطأ: {ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// معالج النقر على زر مسح الاختيار
        /// </summary>
        private void ClearSelectionButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("🗑️ تم النقر على زر مسح الاختيار");
                var result = MessageBox.Show("هل تريد مسح جميع الاختيارات؟", "تأكيد المسح",
                                           MessageBoxButton.YesNo, MessageBoxImage.Question);
                if (result == MessageBoxResult.Yes)
                {
                    MessageBox.Show("تم مسح جميع الاختيارات", "مسح الاختيار",
                                  MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في زر مسح الاختيار: {ex.Message}");
                MessageBox.Show($"خطأ: {ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// معالج النقر على زر إغلاق
        /// </summary>
        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("❌ تم النقر على زر إغلاق");
                Close();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في زر إغلاق: {ex.Message}");
                Close();
            }
        }

        /// <summary>
        /// معالج إغلاق النافذة
        /// </summary>
        protected override void OnClosed(EventArgs e)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("🔒 تم إغلاق نافذة نظام إرسال الرسائل للسائقين المتنافسين");
                base.OnClosed(e);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في إغلاق النافذة: {ex.Message}");
                base.OnClosed(e);
            }
        }
    }
}
