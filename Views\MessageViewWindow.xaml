<Window x:Class="SFDSystem.Views.MessageViewWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="📱 عرض نص الرسالة - النظام المتطور"
        Height="800" Width="1000"
        Background="#F8FAFC"
        FlowDirection="RightToLeft"
        WindowStartupLocation="CenterScreen"
        ResizeMode="CanResize"
        Icon="pack://application:,,,/Resources/message-icon.png"
        MinHeight="600" MinWidth="800">

    <Window.Resources>
        <!-- نمط الأزرار الحديث -->
        <Style x:Key="ModernButtonStyle" TargetType="Button">
            <Setter Property="Background">
                <Setter.Value>
                    <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                        <GradientStop Color="#4F46E5" Offset="0"/>
                        <GradientStop Color="#7C3AED" Offset="0.5"/>
                        <GradientStop Color="#EC4899" Offset="1"/>
                    </LinearGradientBrush>
                </Setter.Value>
            </Setter>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="25,12"/>
            <Setter Property="Margin" Value="10"/>
            <Setter Property="FontSize" Value="16"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="#6366F1" BlurRadius="20" ShadowDepth="6" Opacity="0.4"/>
                </Setter.Value>
            </Setter>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" 
                                CornerRadius="15" 
                                BorderThickness="{TemplateBinding BorderThickness}"
                                BorderBrush="{TemplateBinding BorderBrush}">
                            <ContentPresenter HorizontalAlignment="Center" 
                                            VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background">
                                    <Setter.Value>
                                        <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                                            <GradientStop Color="#3730A3" Offset="0"/>
                                            <GradientStop Color="#6B21A8" Offset="0.5"/>
                                            <GradientStop Color="#BE185D" Offset="1"/>
                                        </LinearGradientBrush>
                                    </Setter.Value>
                                </Setter>
                                <Setter Property="Effect">
                                    <Setter.Value>
                                        <DropShadowEffect Color="#6366F1" BlurRadius="30" ShadowDepth="10" Opacity="0.6"/>
                                    </Setter.Value>
                                </Setter>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="RenderTransform">
                                    <Setter.Value>
                                        <ScaleTransform ScaleX="0.96" ScaleY="0.96"/>
                                    </Setter.Value>
                                </Setter>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- نمط النصوص -->
        <Style x:Key="HeaderTextStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="28"/>
            <Setter Property="FontWeight" Value="Black"/>
            <Setter Property="Foreground">
                <Setter.Value>
                    <LinearGradientBrush StartPoint="0,0" EndPoint="1,0">
                        <GradientStop Color="#1E293B" Offset="0"/>
                        <GradientStop Color="#475569" Offset="1"/>
                    </LinearGradientBrush>
                </Setter.Value>
            </Setter>
            <Setter Property="TextAlignment" Value="Center"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="#94A3B8" BlurRadius="5" ShadowDepth="2" Opacity="0.3"/>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- نمط مربع النص -->
        <Style x:Key="MessageTextBoxStyle" TargetType="TextBox">
            <Setter Property="Background" Value="White"/>
            <Setter Property="BorderThickness" Value="2"/>
            <Setter Property="BorderBrush" Value="#E2E8F0"/>
            <Setter Property="Padding" Value="20"/>
            <Setter Property="FontSize" Value="16"/>
            <Setter Property="FontFamily" Value="Segoe UI"/>
            <Setter Property="LineHeight" Value="24"/>
            <Setter Property="TextWrapping" Value="Wrap"/>
            <Setter Property="AcceptsReturn" Value="True"/>
            <Setter Property="VerticalScrollBarVisibility" Value="Auto"/>
            <Setter Property="IsReadOnly" Value="True"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="TextBox">
                        <Border Background="{TemplateBinding Background}" 
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="15">
                            <ScrollViewer x:Name="PART_ContentHost" 
                                        Padding="{TemplateBinding Padding}"
                                        VerticalScrollBarVisibility="{TemplateBinding VerticalScrollBarVisibility}"/>
                        </Border>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>

    <Grid>
        <!-- الخلفية المتدرجة -->
        <Border>
            <Border.Background>
                <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                    <GradientStop Color="#F8FAFC" Offset="0"/>
                    <GradientStop Color="#E2E8F0" Offset="0.3"/>
                    <GradientStop Color="#F1F5F9" Offset="0.7"/>
                    <GradientStop Color="#F8FAFC" Offset="1"/>
                </LinearGradientBrush>
            </Border.Background>
        </Border>

        <!-- المحتوى الرئيسي -->
        <Grid Margin="40">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- العنوان -->
            <Border Grid.Row="0" CornerRadius="25" Padding="30,20" Margin="0,0,0,30"
                    BorderThickness="2" BorderBrush="#E2E8F0">
                <Border.Background>
                    <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                        <GradientStop Color="#667eea" Offset="0"/>
                        <GradientStop Color="#764ba2" Offset="0.5"/>
                        <GradientStop Color="#f093fb" Offset="1"/>
                    </LinearGradientBrush>
                </Border.Background>
                <Border.Effect>
                    <DropShadowEffect Color="#667eea" BlurRadius="40" ShadowDepth="12" Opacity="0.4"/>
                </Border.Effect>
                <StackPanel>
                    <TextBlock Text="📱 عرض نص الرسالة" FontSize="32" FontWeight="Black"
                              Foreground="White" TextAlignment="Center">
                        <TextBlock.Effect>
                            <DropShadowEffect Color="#000000" BlurRadius="8" ShadowDepth="3" Opacity="0.3"/>
                        </TextBlock.Effect>
                    </TextBlock>
                    <TextBlock x:Name="DriverNameText" Text="السائق: [اسم السائق]"
                              FontSize="20" FontWeight="SemiBold" Foreground="#F1F5F9"
                              HorizontalAlignment="Center" Margin="0,15,0,0">
                        <TextBlock.Effect>
                            <DropShadowEffect Color="#000000" BlurRadius="5" ShadowDepth="2" Opacity="0.2"/>
                        </TextBlock.Effect>
                    </TextBlock>
                </StackPanel>
            </Border>

            <!-- محتوى الرسالة -->
            <Border Grid.Row="1" Background="White" CornerRadius="25" Padding="30" 
                    BorderThickness="2" BorderBrush="#E2E8F0" Margin="0,0,0,30">
                <Border.Effect>
                    <DropShadowEffect Color="#CBD5E1" BlurRadius="30" ShadowDepth="8" Opacity="0.3"/>
                </Border.Effect>
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <!-- عنوان المحتوى -->
                    <Border Grid.Row="0" CornerRadius="15" Padding="20,15" Margin="0,0,0,20"
                            BorderThickness="2">
                        <Border.Background>
                            <LinearGradientBrush StartPoint="0,0" EndPoint="1,0">
                                <GradientStop Color="#F8FAFC" Offset="0"/>
                                <GradientStop Color="#E2E8F0" Offset="0.5"/>
                                <GradientStop Color="#F8FAFC" Offset="1"/>
                            </LinearGradientBrush>
                        </Border.Background>
                        <Border.BorderBrush>
                            <LinearGradientBrush StartPoint="0,0" EndPoint="1,0">
                                <GradientStop Color="#CBD5E1" Offset="0"/>
                                <GradientStop Color="#94A3B8" Offset="0.5"/>
                                <GradientStop Color="#CBD5E1" Offset="1"/>
                            </LinearGradientBrush>
                        </Border.BorderBrush>
                        <TextBlock Text="📝 محتوى الرسالة" FontSize="22" FontWeight="Bold"
                                  Foreground="#1E293B" HorizontalAlignment="Center">
                            <TextBlock.Effect>
                                <DropShadowEffect Color="#94A3B8" BlurRadius="3" ShadowDepth="1" Opacity="0.3"/>
                            </TextBlock.Effect>
                        </TextBlock>
                    </Border>

                    <!-- مربع النص -->
                    <TextBox x:Name="MessageContentTextBox" Grid.Row="1"
                            Style="{StaticResource MessageTextBoxStyle}"
                            MinHeight="350"
                            FontSize="15"
                            LineHeight="26"/>
                </Grid>
            </Border>

            <!-- الأزرار -->
            <Border Grid.Row="2" Background="White" CornerRadius="25" Padding="30" 
                    BorderThickness="2" BorderBrush="#E2E8F0">
                <Border.Effect>
                    <DropShadowEffect Color="#CBD5E1" BlurRadius="30" ShadowDepth="8" Opacity="0.3"/>
                </Border.Effect>
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <!-- زر نسخ -->
                    <Button Grid.Column="0" Style="{StaticResource ModernButtonStyle}"
                            Click="CopyButton_Click" Height="60">
                        <StackPanel Orientation="Horizontal">
                            <Border Background="White" CornerRadius="25" Width="40" Height="40" Margin="0,0,15,0">
                                <TextBlock Text="📋" FontSize="22" Foreground="#4F46E5"
                                          VerticalAlignment="Center" HorizontalAlignment="Center"/>
                            </Border>
                            <TextBlock Text="نسخ النص" FontSize="18" FontWeight="Bold" VerticalAlignment="Center"/>
                        </StackPanel>
                    </Button>

                    <!-- زر طباعة -->
                    <Button Grid.Column="1" Style="{StaticResource ModernButtonStyle}"
                            Click="PrintButton_Click" Height="60">
                        <StackPanel Orientation="Horizontal">
                            <Border Background="White" CornerRadius="25" Width="40" Height="40" Margin="0,0,15,0">
                                <TextBlock Text="🖨️" FontSize="22" Foreground="#4F46E5"
                                          VerticalAlignment="Center" HorizontalAlignment="Center"/>
                            </Border>
                            <TextBlock Text="طباعة" FontSize="18" FontWeight="Bold" VerticalAlignment="Center"/>
                        </StackPanel>
                    </Button>

                    <!-- زر إغلاق -->
                    <Button Grid.Column="2" Style="{StaticResource ModernButtonStyle}"
                            Click="CloseButton_Click" Height="60">
                        <StackPanel Orientation="Horizontal">
                            <Border Background="White" CornerRadius="25" Width="40" Height="40" Margin="0,0,15,0">
                                <TextBlock Text="❌" FontSize="22" Foreground="#4F46E5"
                                          VerticalAlignment="Center" HorizontalAlignment="Center"/>
                            </Border>
                            <TextBlock Text="إغلاق" FontSize="18" FontWeight="Bold" VerticalAlignment="Center"/>
                        </StackPanel>
                    </Button>
                </Grid>
            </Border>
        </Grid>
    </Grid>
</Window>
