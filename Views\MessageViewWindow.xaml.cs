using System;
using System.Windows;
using System.Windows.Controls;

namespace SFDSystem.Views
{
    /// <summary>
    /// نافذة عرض نص الرسالة بتصميم حديث
    /// </summary>
    public partial class MessageViewWindow : Window
    {
        public MessageViewWindow()
        {
            InitializeComponent();
        }

        /// <summary>
        /// إنشاء النافذة مع محتوى الرسالة واسم السائق
        /// </summary>
        /// <param name="driverName">اسم السائق</param>
        /// <param name="messageContent">محتوى الرسالة</param>
        public MessageViewWindow(string driverName, string messageContent) : this()
        {
            DriverNameText.Text = $"السائق: {driverName}";
            MessageContentTextBox.Text = messageContent;
        }

        /// <summary>
        /// نسخ النص إلى الحافظة
        /// </summary>
        private void CopyButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (!string.IsNullOrWhiteSpace(MessageContentTextBox.Text))
                {
                    Clipboard.SetText(MessageContentTextBox.Text);
                    MessageBox.Show("✅ تم نسخ النص إلى الحافظة بنجاح!", "نسخ النص",
                                  MessageBoxButton.OK, MessageBoxImage.Information);
                }
                else
                {
                    MessageBox.Show("⚠️ لا يوجد نص لنسخه!", "تنبيه",
                                  MessageBoxButton.OK, MessageBoxImage.Warning);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"❌ خطأ في نسخ النص:\n\n{ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// طباعة النص
        /// </summary>
        private void PrintButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (!string.IsNullOrWhiteSpace(MessageContentTextBox.Text))
                {
                    var printDialog = new System.Windows.Controls.PrintDialog();
                    if (printDialog.ShowDialog() == true)
                    {
                        // إنشاء مستند للطباعة
                        var flowDocument = new System.Windows.Documents.FlowDocument();
                        
                        // إضافة العنوان
                        var titleParagraph = new System.Windows.Documents.Paragraph();
                        titleParagraph.Inlines.Add(new System.Windows.Documents.Run("نص الرسالة")
                        {
                            FontSize = 20,
                            FontWeight = FontWeights.Bold
                        });
                        flowDocument.Blocks.Add(titleParagraph);

                        // إضافة اسم السائق
                        var driverParagraph = new System.Windows.Documents.Paragraph();
                        driverParagraph.Inlines.Add(new System.Windows.Documents.Run(DriverNameText.Text)
                        {
                            FontSize = 16,
                            FontWeight = FontWeights.SemiBold
                        });
                        flowDocument.Blocks.Add(driverParagraph);

                        // إضافة خط فاصل
                        var separatorParagraph = new System.Windows.Documents.Paragraph();
                        separatorParagraph.Inlines.Add(new System.Windows.Documents.Run("─────────────────────────────────────"));
                        flowDocument.Blocks.Add(separatorParagraph);

                        // إضافة محتوى الرسالة
                        var contentParagraph = new System.Windows.Documents.Paragraph();
                        contentParagraph.Inlines.Add(new System.Windows.Documents.Run(MessageContentTextBox.Text)
                        {
                            FontSize = 14
                        });
                        flowDocument.Blocks.Add(contentParagraph);

                        // تحديد خصائص المستند
                        flowDocument.PageHeight = printDialog.PrintableAreaHeight;
                        flowDocument.PageWidth = printDialog.PrintableAreaWidth;
                        flowDocument.PagePadding = new Thickness(50);
                        flowDocument.ColumnGap = 0;
                        flowDocument.ColumnWidth = printDialog.PrintableAreaWidth;

                        // طباعة المستند
                        System.Windows.Documents.IDocumentPaginatorSource idpSource = flowDocument;
                        printDialog.PrintDocument(idpSource.DocumentPaginator, "نص الرسالة");

                        MessageBox.Show("✅ تم إرسال المستند للطباعة بنجاح!", "طباعة",
                                      MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                }
                else
                {
                    MessageBox.Show("⚠️ لا يوجد نص للطباعة!", "تنبيه",
                                  MessageBoxButton.OK, MessageBoxImage.Warning);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"❌ خطأ في الطباعة:\n\n{ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// إغلاق النافذة
        /// </summary>
        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            this.Close();
        }
    }
}
