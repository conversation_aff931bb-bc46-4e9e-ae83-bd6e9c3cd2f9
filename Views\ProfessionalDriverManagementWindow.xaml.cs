using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using DriverManagementSystem.ViewModels;
using DriverManagementSystem.Services;

namespace DriverManagementSystem.Views
{
    public partial class ProfessionalDriverManagementWindow : Window
    {
        private readonly DriverManagementSystem.ViewModels.ProfessionalMessagesViewModel _viewModel;

        // خاصية لحفظ السائقين المحددين
        public List<DriverModel> ConfirmedSelectedDrivers { get; private set; } = new List<DriverModel>();

        // خاصية لحفظ نافذة الرسائل الأصلية
        public SFDSystem.Views.ProfessionalMessagesWindow ParentMessagesWindow { get; set; }

        public ProfessionalDriverManagementWindow()
        {
            try
            {
                InitializeComponent();

                System.Diagnostics.Debug.WriteLine("🏗️ ProfessionalDriverManagementWindow: بدء إنشاء النافذة");

                // إنشاء وتعيين ViewModel بدون تحميل تلقائي لتجنب التكرار
                _viewModel = new DriverManagementSystem.ViewModels.ProfessionalMessagesViewModel(autoLoad: false);
                DataContext = _viewModel;

                // تحميل البيانات مرة واحدة فقط
                Loaded += async (s, e) => await LoadDataAsync();

                // إضافة معالج إغلاق النافذة للتنظيف الآمن
                Closing += ProfessionalDriverManagementWindow_Closing;
                Closed += ProfessionalDriverManagementWindow_Closed;

                System.Diagnostics.Debug.WriteLine("✅ ProfessionalDriverManagementWindow: تم إنشاء النافذة بنجاح");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في إنشاء ProfessionalDriverManagementWindow: {ex.Message}");
                MessageBox.Show($"خطأ في تهيئة نافذة إدارة السائقين: {ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// إنشاء نافذة إدارة السائقين مع بيانات زيارة محددة
        /// </summary>
        /// <param name="visit">الزيارة المحددة</param>
        public ProfessionalDriverManagementWindow(DriverManagementSystem.Models.FieldVisit visit) : this()
        {
            if (visit != null)
            {
                // تحميل بيانات الزيارة
                _viewModel.LoadVisitData(visit);

                System.Diagnostics.Debug.WriteLine($"🔍 ProfessionalDriverManagementWindow created for visit: {visit.VisitNumber}");
            }
        }

        /// <summary>
        /// إنشاء نافذة إدارة السائقين مع نافذة الرسائل الأصلية
        /// </summary>
        /// <param name="parentMessagesWindow">نافذة الرسائل الأصلية</param>
        /// <param name="visit">الزيارة المحددة</param>
        public ProfessionalDriverManagementWindow(SFDSystem.Views.ProfessionalMessagesWindow parentMessagesWindow, DriverManagementSystem.Models.FieldVisit visit = null) : this()
        {
            ParentMessagesWindow = parentMessagesWindow;

            if (visit != null)
            {
                // تحميل بيانات الزيارة
                _viewModel.LoadVisitData(visit);

                System.Diagnostics.Debug.WriteLine($"🔍 ProfessionalDriverManagementWindow created for visit: {visit.VisitNumber} with parent window");
            }
        }

        /// <summary>
        /// تحميل البيانات بشكل آمن ونظيف من قاعدة البيانات
        /// </summary>
        private async Task LoadDataAsync()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("🔄 LoadDataAsync: بدء تحميل البيانات بشكل آمن ونظيف");

                // إعادة تحميل البيانات من قاعدة البيانات بشكل نظيف
                await _viewModel.RefreshDataFromDatabase();

                // استعادة الاختيار المحفوظ
                RestoreConfirmedSelection();

                // تحديث العدادات
                // UpdateStatistics(); // سيتم تحديثها تلقائياً في ViewModel

                System.Diagnostics.Debug.WriteLine($"✅ LoadDataAsync: تم تحميل {_viewModel.AllDrivers.Count} سائق بنجاح");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ LoadDataAsync Error: {ex.Message}");
                MessageBox.Show($"خطأ في تحميل بيانات السائقين: {ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// إنشاء نافذة إدارة السائقين بشكل آمن ونظيف
        /// </summary>
        public static ProfessionalDriverManagementWindow CreateSafeWindow()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("🏗️ CreateSafeWindow: إنشاء نافذة إدارة السائقين بشكل آمن");

                var window = new ProfessionalDriverManagementWindow();

                System.Diagnostics.Debug.WriteLine("✅ CreateSafeWindow: تم إنشاء النافذة بنجاح");
                return window;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ CreateSafeWindow Error: {ex.Message}");
                throw;
            }
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("🔒 CloseButton_Click: إغلاق النافذة");
                DialogResult = false;
                Close();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Error closing window: {ex.Message}");
                Close();
            }
        }

        private void VehicleFilter_Changed(object sender, RoutedEventArgs e)
        {
            try
            {
                if (sender is CheckBox checkBox && checkBox.Tag != null)
                {
                    var vehicleType = checkBox.Tag.ToString();
                    var isChecked = checkBox.IsChecked ?? false;

                    System.Diagnostics.Debug.WriteLine($"🚗 Vehicle filter changed: {vehicleType} = {isChecked}");

                    // تطبيق الفلترة
                    ApplyVehicleFilter();
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Error in vehicle filter: {ex.Message}");
            }
        }

        /// <summary>
        /// تحديث الاختيار عند تغيير التحديد في DataGrid
        /// </summary>
        private void DriversDataGrid_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            try
            {
                // تحديث قائمة السائقين المحددين في ViewModel
                if (_viewModel != null)
                {
                    // تحديث قائمة السائقين المحددين
                    UpdateSelectedDrivers();

                    System.Diagnostics.Debug.WriteLine($"📊 Selection updated - Selected: {_viewModel.SelectedDriversCount}");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Error updating selection: {ex.Message}");
            }
        }

        /// <summary>
        /// تحديث قائمة السائقين المحددين
        /// </summary>
        private void UpdateSelectedDrivers()
        {
            try
            {
                if (_viewModel != null)
                {
                    // مسح القائمة الحالية
                    _viewModel.SelectedDrivers.Clear();

                    // إضافة السائقين المحددين
                    foreach (var driver in _viewModel.AllDrivers.Where(d => d.IsSelected))
                    {
                        _viewModel.SelectedDrivers.Add(driver);
                    }

                    // تحديث الإحصائيات - استخدام reflection لاستدعاء OnPropertyChanged
                    var onPropertyChangedMethod = _viewModel.GetType().GetMethod("OnPropertyChanged",
                        System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);

                    onPropertyChangedMethod?.Invoke(_viewModel, new object[] { nameof(_viewModel.SelectedDriversCount) });
                    onPropertyChangedMethod?.Invoke(_viewModel, new object[] { nameof(_viewModel.TotalDriversCount) });
                    onPropertyChangedMethod?.Invoke(_viewModel, new object[] { nameof(_viewModel.AvailableDriversCount) });

                    System.Diagnostics.Debug.WriteLine($"✅ Updated selected drivers: {_viewModel.SelectedDrivers.Count}");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Error updating selected drivers: {ex.Message}");
            }
        }

        /// <summary>
        /// تطبيق فلترة السيارات
        /// </summary>
        private void ApplyVehicleFilter()
        {
            try
            {
                // الحصول على أنواع السيارات المحددة
                var selectedVehicleTypes = new List<string>();

                // فحص كل checkbox بالاسم
                if (ForshanalFilter.IsChecked == true) selectedVehicleTypes.Add("فورشنال");
                if (KanterFilter.IsChecked == true) selectedVehicleTypes.Add("كنتر");
                if (HiluxFilter.IsChecked == true) selectedVehicleTypes.Add("هيلوكس");
                if (BusFilter.IsChecked == true) selectedVehicleTypes.Add("حافلة");
                if (PradoFilter.IsChecked == true) selectedVehicleTypes.Add("برادو");

                // تطبيق الفلترة على ViewModel
                if (_viewModel != null)
                {
                    _viewModel.SelectedVehicleTypes = selectedVehicleTypes;
                    System.Diagnostics.Debug.WriteLine($"✅ Applied vehicle filter: {string.Join(", ", selectedVehicleTypes)}");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Error applying vehicle filter: {ex.Message}");
            }
        }

        /// <summary>
        /// البحث عن عناصر فرعية من نوع معين
        /// </summary>
        private static IEnumerable<T> FindVisualChildren<T>(DependencyObject depObj) where T : DependencyObject
        {
            if (depObj != null)
            {
                for (int i = 0; i < VisualTreeHelper.GetChildrenCount(depObj); i++)
                {
                    DependencyObject child = VisualTreeHelper.GetChild(depObj, i);
                    if (child != null && child is T)
                    {
                        yield return (T)child;
                    }

                    foreach (T childOfChild in FindVisualChildren<T>(child))
                    {
                        yield return childOfChild;
                    }
                }
            }
        }

        /// <summary>
        /// مسح نص البحث
        /// </summary>
        private void ClearSearch_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                _viewModel.SearchText = "";
                System.Diagnostics.Debug.WriteLine("✅ Search text cleared");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Error clearing search: {ex.Message}");
            }
        }

        private void SelectAllDrivers_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                _viewModel.SelectAllCommand?.Execute(null);
                System.Diagnostics.Debug.WriteLine($"✅ Selected all drivers - Total: {_viewModel.SelectedDrivers.Count}");
                MessageBox.Show($"تم تحديد جميع السائقين ({_viewModel.FilteredDrivers.Count} سائق)", "تم التحديد",
                              MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Error selecting all drivers: {ex.Message}");
                MessageBox.Show($"خطأ في تحديد السائقين: {ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ClearSelection_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                _viewModel.ClearSelectionCommand?.Execute(null);
                System.Diagnostics.Debug.WriteLine("✅ Cleared all selections");
                MessageBox.Show("تم إلغاء تحديد جميع السائقين", "تم الإلغاء",
                              MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Error clearing selections: {ex.Message}");
                MessageBox.Show($"خطأ في إلغاء التحديد: {ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void RefreshData_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                await LoadDataAsync();
                System.Diagnostics.Debug.WriteLine($"✅ Data refreshed - Total drivers: {_viewModel.AllDrivers.Count}");
                MessageBox.Show($"تم تحديث البيانات بنجاح\nإجمالي السائقين: {_viewModel.AllDrivers.Count}", "تم التحديث",
                              MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Error refreshing data: {ex.Message}");
                MessageBox.Show($"خطأ في تحديث البيانات: {ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// استعادة الاختيار المحفوظ
        /// </summary>
        private void RestoreConfirmedSelection()
        {
            try
            {
                if (ConfirmedSelectedDrivers?.Count > 0 && _viewModel != null)
                {
                    foreach (var confirmedDriver in ConfirmedSelectedDrivers)
                    {
                        var driver = _viewModel.AllDrivers.FirstOrDefault(d => d.DriverCode == confirmedDriver.DriverCode);
                        if (driver != null)
                        {
                            driver.IsSelected = true;
                        }
                    }

                    UpdateSelectedDrivers();
                    System.Diagnostics.Debug.WriteLine($"✅ Restored {ConfirmedSelectedDrivers.Count} confirmed selections");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Error restoring confirmed selection: {ex.Message}");
            }
        }

        private async void ConfirmSelectionButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // تحديث قائمة السائقين المحددين أولاً
                UpdateSelectedDrivers();

                var selectedDrivers = _viewModel.SelectedDrivers;
                if (selectedDrivers?.Count == 0)
                {
                    MessageBox.Show("يرجى اختيار سائق واحد على الأقل", "تنبيه",
                                  MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                var result = MessageBox.Show($"هل تريد فتح نافذة الرسائل للسائقين المحددين؟\n\nعدد السائقين المحددين: {selectedDrivers.Count}",
                                           "تأكيد الاختيار", MessageBoxButton.YesNo, MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    System.Diagnostics.Debug.WriteLine($"✅ Confirming selection with {selectedDrivers.Count} selected drivers");

                    // حفظ السائقين المحددين
                    ConfirmedSelectedDrivers.Clear();
                    ConfirmedSelectedDrivers.AddRange(selectedDrivers.ToList());

                    // إذا كانت هناك نافذة رسائل أصلية، ارجع إليها
                    if (ParentMessagesWindow != null)
                    {
                        System.Diagnostics.Debug.WriteLine("🔄 Returning to parent messages window");

                        // تحديث السائقين في النافذة الأصلية بشكل آمن
                        if (ParentMessagesWindow.DataContext is DriverManagementSystem.ViewModels.ProfessionalMessagesViewModel parentViewModel)
                        {
                            // إعادة تحميل البيانات من قاعدة البيانات أولاً لضمان عدم التكرار
                            await parentViewModel.RefreshDataFromDatabase();

                            // تحديد السائقين المختارين فقط
                            var selectedDriverCodes = selectedDrivers.Select(d => d.DriverCode?.Trim()?.ToLower()).Where(c => !string.IsNullOrWhiteSpace(c)).ToHashSet();

                            foreach (var driver in parentViewModel.AllDrivers)
                            {
                                driver.IsSelected = selectedDriverCodes.Contains(driver.DriverCode?.Trim()?.ToLower());
                            }

                            // تطبيق الفلترة وتحديث العدادات
                            parentViewModel.FilterDrivers();

                            // تحديث النص المعروض
                            parentViewModel.NotifyPropertyChanged(nameof(parentViewModel.SelectedDriversDisplayText));

                            System.Diagnostics.Debug.WriteLine($"✅ تم تحديث النافذة الأصلية مع {selectedDrivers.Count} سائق محدد");
                        }

                        // إغلاق هذه النافذة والعودة للأصلية
                        this.Close();

                        // إظهار النافذة الأصلية
                        ParentMessagesWindow.Show();
                        ParentMessagesWindow.Activate();
                    }
                    else
                    {
                        // إذا لم تكن هناك نافذة أصلية، افتح نافذة جديدة (السلوك القديم)
                        System.Diagnostics.Debug.WriteLine("🆕 Opening new messages window");

                        SFDSystem.Views.ProfessionalMessagesWindow messagesWindow;

                        // إذا كانت هناك زيارة محددة، مرر بياناتها
                        if (_viewModel.SelectedVisit != null)
                        {
                            messagesWindow = new SFDSystem.Views.ProfessionalMessagesWindow(_viewModel.SelectedVisit);
                        }
                        else
                        {
                            messagesWindow = new SFDSystem.Views.ProfessionalMessagesWindow();
                        }

                        if (messagesWindow.DataContext is DriverManagementSystem.ViewModels.ProfessionalMessagesViewModel messagesViewModel)
                        {
                            // نسخ السائقين المحددين إلى نافذة الرسائل
                            messagesViewModel.AllDrivers.Clear();
                            foreach (var driver in selectedDrivers)
                            {
                                messagesViewModel.AllDrivers.Add(driver);
                                driver.IsSelected = true;
                            }
                            messagesViewModel.FilterDrivers();

                            // تحديث النص المعروض
                            messagesViewModel.NotifyPropertyChanged(nameof(messagesViewModel.SelectedDriversDisplayText));
                        }

                        messagesWindow.ShowDialog();

                        // تحديث البيانات بعد إغلاق نافذة الرسائل
                        await LoadDataAsync();
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Error confirming selection: {ex.Message}");
                MessageBox.Show($"خطأ في تأكيد الاختيار: {ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                DialogResult = false;
                Close();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Error canceling: {ex.Message}");
                Close();
            }
        }

        /// <summary>
        /// اتصال بالسائق
        /// </summary>
        private void CallDriver_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var selectedDriver = DriversDataGrid.SelectedItem as DriverManagementSystem.ViewModels.DriverModel;
                if (selectedDriver != null && !string.IsNullOrEmpty(selectedDriver.PhoneNumber))
                {
                    var result = MessageBox.Show($"هل تريد الاتصال بالسائق {selectedDriver.Name}؟\nرقم الهاتف: {selectedDriver.PhoneNumber}",
                                               "تأكيد الاتصال", MessageBoxButton.YesNo, MessageBoxImage.Question);

                    if (result == MessageBoxResult.Yes)
                    {
                        // فتح تطبيق الهاتف
                        System.Diagnostics.Process.Start(new System.Diagnostics.ProcessStartInfo
                        {
                            FileName = $"tel:{selectedDriver.PhoneNumber}",
                            UseShellExecute = true
                        });
                    }
                }
                else
                {
                    MessageBox.Show("لا يوجد رقم هاتف متاح لهذا السائق", "تنبيه",
                                  MessageBoxButton.OK, MessageBoxImage.Warning);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Error calling driver: {ex.Message}");
                MessageBox.Show($"خطأ في الاتصال: {ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// إرسال رسالة للسائق
        /// </summary>
        private void MessageDriver_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var selectedDriver = DriversDataGrid.SelectedItem as DriverManagementSystem.ViewModels.DriverModel;
                if (selectedDriver != null)
                {
                    // تحديد السائق وفتح نافذة الرسائل
                    selectedDriver.IsSelected = true;

                    var messagesWindow = new SFDSystem.Views.ProfessionalMessagesWindow();
                    if (messagesWindow.DataContext is DriverManagementSystem.ViewModels.ProfessionalMessagesViewModel messagesViewModel)
                    {
                        // إضافة السائق المحدد فقط
                        messagesViewModel.AllDrivers.Clear();
                        messagesViewModel.AllDrivers.Add(selectedDriver);
                        messagesViewModel.FilterDrivers();
                    }

                    messagesWindow.ShowDialog();
                }
                else
                {
                    MessageBox.Show("يرجى اختيار سائق أولاً", "تنبيه",
                                  MessageBoxButton.OK, MessageBoxImage.Warning);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Error messaging driver: {ex.Message}");
                MessageBox.Show($"خطأ في إرسال الرسالة: {ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// معالج إغلاق النافذة - التحقق من التغييرات
        /// </summary>
        private void ProfessionalDriverManagementWindow_Closing(object sender, System.ComponentModel.CancelEventArgs e)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("🔒 ProfessionalDriverManagementWindow_Closing: بدء إغلاق النافذة");

                // يمكن إضافة تحقق من التغييرات غير المحفوظة هنا إذا لزم الأمر
                // مثلاً: إذا كان هناك سائقين محددين ولم يتم تأكيد الاختيار

                System.Diagnostics.Debug.WriteLine("✅ ProfessionalDriverManagementWindow_Closing: تم التحقق من الإغلاق");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ ProfessionalDriverManagementWindow_Closing Error: {ex.Message}");
            }
        }

        /// <summary>
        /// معالج إغلاق النافذة - التنظيف الشامل والآمن
        /// </summary>
        private void ProfessionalDriverManagementWindow_Closed(object sender, EventArgs e)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("🧹 ProfessionalDriverManagementWindow_Closed: بدء التنظيف الشامل والآمن");

                // تنظيف ViewModel بشكل آمن
                if (_viewModel != null)
                {
                    _viewModel.Dispose();
                    System.Diagnostics.Debug.WriteLine("✅ تم تنظيف ViewModel بنجاح");
                }

                // تنظيف DataContext
                DataContext = null;

                // تنظيف المراجع
                ConfirmedSelectedDrivers?.Clear();
                ParentMessagesWindow = null;

                // تنظيف الذاكرة
                GC.Collect();
                GC.WaitForPendingFinalizers();

                System.Diagnostics.Debug.WriteLine("✅ ProfessionalDriverManagementWindow_Closed: تم التنظيف الشامل والآمن بنجاح");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ ProfessionalDriverManagementWindow_Closed Error: {ex.Message}");
            }
        }
    }
}
