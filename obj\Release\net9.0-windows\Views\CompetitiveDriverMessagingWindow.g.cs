﻿#pragma checksum "..\..\..\..\Views\CompetitiveDriverMessagingWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "E76A494267E9D16413D53FA32B5F2D828C82401F"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace DriverManagementSystem.Views {
    
    
    /// <summary>
    /// CompetitiveDriverMessagingWindow
    /// </summary>
    public partial class CompetitiveDriverMessagingWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 326 "..\..\..\..\Views\CompetitiveDriverMessagingWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid DriversDataGrid;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.7.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/SFDSystem;V2.0.0.0;component/views/competitivedrivermessagingwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Views\CompetitiveDriverMessagingWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.7.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            
            #line 267 "..\..\..\..\Views\CompetitiveDriverMessagingWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.SelectDriversButton_Click);
            
            #line default
            #line hidden
            return;
            case 2:
            this.DriversDataGrid = ((System.Windows.Controls.DataGrid)(target));
            return;
            case 3:
            
            #line 428 "..\..\..\..\Views\CompetitiveDriverMessagingWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.SendGroupMessagesButton_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            
            #line 439 "..\..\..\..\Views\CompetitiveDriverMessagingWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.TrackResponsesButton_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            
            #line 450 "..\..\..\..\Views\CompetitiveDriverMessagingWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.MessageSettingsButton_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            
            #line 485 "..\..\..\..\Views\CompetitiveDriverMessagingWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.RefreshDataButton_Click);
            
            #line default
            #line hidden
            return;
            case 7:
            
            #line 496 "..\..\..\..\Views\CompetitiveDriverMessagingWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ExportReportButton_Click);
            
            #line default
            #line hidden
            return;
            case 8:
            
            #line 507 "..\..\..\..\Views\CompetitiveDriverMessagingWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ClearSelectionButton_Click);
            
            #line default
            #line hidden
            return;
            case 9:
            
            #line 518 "..\..\..\..\Views\CompetitiveDriverMessagingWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.CloseButton_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

